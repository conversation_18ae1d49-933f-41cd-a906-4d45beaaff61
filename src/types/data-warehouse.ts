export interface Domain {
  id: number;
  name: string;
  shortName: string;
  description?: string;
  layeringType: number;
  ownPerson: number;
  ownPersonName: string;
  creator: number;
  creatorName: string;
  updater: number;
  updaterName: string;
  createTime: number;
  updateTime: number;
  version: number;
  deleted: number;
}

export interface Layer {
  id: number;
  name: string;
  shortName: string;
  ownPerson: number;
  ownPersonName: string;
  creator: number;
  creatorName: string;
  updater: number;
  updaterName: string;
  createTime: number;
  updateTime: number;
  version: number;
  deleted: number;
  domains: Domain[];
}
