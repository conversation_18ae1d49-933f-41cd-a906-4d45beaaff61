<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-05-07 09:20:20
 * @LastEditors: zhangzao
 * @LastEditTime: 2025-05-12 09:36:19
 * @Description: 请填写简介
 * @FilePath: /big_data/src/views/data-warehouse/tables/detail.vue
-->
<template>
  <div class="table-detail-container">
    <div class="page-header">
      <h2>数据分仓-表管理</h2>
    </div>
    <div class="page-content">
      <!-- 左侧表列表 -->
      <div class="table-list">
        <div class="header">
          <div class="action-buttons">
            <el-button
              type="primary"
              size="default"
              @click="showCreateTable = true"
            >
              <template #icon>
                <el-icon class="icon-btn"><Plus /></el-icon>
              </template>
              新建表
            </el-button>
          </div>
        </div>
        <div class="list">
          <el-tree
            :data="treeData"
            :props="defaultProps"
            :default-expanded-keys="['DIM']"
            node-key="id"
            @node-click="handleNodeClick"
          >
            <template #default="{ node, data }">
              <span
                :class="[
                  'custom-tree-node',
                  { active: currentTable === data.id }
                ]"
              >
                {{ node.label }}
              </span>
            </template>
          </el-tree>
        </div>
      </div>

      <!-- 右侧表详情 -->
      <div class="table-info">
        <el-card class="box-card">
          <!-- 表操作按钮 -->
          <div v-if="currentTable" class="table-actions">
            <el-button type="primary" :icon="Edit" @click="handleEditTable">
              编辑表
            </el-button>
            <el-button type="danger" :icon="Delete" @click="handleDeleteTable">
              删除表
            </el-button>
          </div>

          <div v-if="!currentTable" class="empty-state">
            <el-empty description="请选择一个表" />
          </div>

          <template v-else>
            <!-- 表信息 -->
            <div class="info-section">
              <div class="section-title">表信息</div>
              <div class="table-info-row">
                <div class="info-item">
                  <span class="label">表名：</span>
                  <span class="value">{{ tableInfo?.tableName || "-" }}</span>
                </div>
                <div class="info-item">
                  <span class="label">中文名称：</span>
                  <span class="value">{{ tableInfo?.comment || "-" }}</span>
                </div>
                <div class="info-item">
                  <span class="label">领域：</span>
                  <span class="value">{{ tableInfo?.domain || "-" }}</span>
                </div>
              </div>
            </div>

            <!-- 字段管理 -->
            <div class="field-section">
              <div class="section-title">字段管理</div>
              <el-table
                :data="fieldList"
                class="full-width-table"
                style="width: 100%"
              >
                <el-table-column prop="fieldName" label="字段名称" />
                <el-table-column prop="fieldType" label="数据类型">
                  <template #default="scope">
                    {{ fieldTypeLabels[scope.row.fieldType] }}
                  </template>
                </el-table-column>
                <el-table-column prop="length" label="长度" />
                <el-table-column prop="decimal" label="小数位数" />
                <el-table-column prop="isNull" label="可空">
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.isNull" disabled />
                  </template>
                </el-table-column>
                <el-table-column prop="isPrimary" label="主键">
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.isPrimary" disabled />
                  </template>
                </el-table-column>
                <el-table-column prop="comment" label="注释" />
                <template #empty>
                  <el-empty description="暂无字段数据" />
                </template>
              </el-table>
            </div>
          </template>
        </el-card>
      </div>
    </div>

    <!-- 新建表弹窗组件 -->
    <CreateTableDialog
      ref="createTableDialogRef"
      v-model="showCreateTable"
      :domain-options="domainOptions"
      :edit-data="editTableData"
      @confirm="onCreateTable"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onActivated, onDeactivated } from "vue";
import { useRoute, useRouter } from "vue-router";
import CreateTableDialog from "./CreateTableDialog.vue";
import { Plus, Setting, Edit, Delete } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  getDomainDetail,
  getTableList,
  deleteTable
} from "@/api/data-warehouse";
import type {
  DomainDetail,
  LayerTableInfo,
  DomainTableInfo
} from "@/api/data-warehouse";

const route = useRoute();
const router = useRouter();

// 初始化所有响应式数据
const mode = ref(route.query.mode as string);
const domainId = ref(route.query.domainId as string);
const layerId = ref(route.query.layerId as string);
const searchQuery = ref("");
const showCreateTable = ref(false);
const currentTable = ref<string | null>(null);
const domainOptions = ref<{ label: string; value: number }[]>([]);
const tableInfo = ref<{
  tableName: string;
  comment: string;
  domain: string;
} | null>(null);
const fieldList = ref<any[]>([]);
const treeData = ref<any[]>([]);
const createTableDialogRef = ref<InstanceType<typeof CreateTableDialog> | null>(
  null
);
const editTableData = ref<any | null>(null);

// 字段类型标签映射
const fieldTypeLabels: { [key: string]: string } = {
  float: "浮点数",
  string: "字符串",
  date: "日期",
  boolean: "布尔值",
  integer: "整数",
  bigint: "大整数",
  serializable: "可序列化",
  binary: "二进制",
  timestamp: "时间戳",
  time: "时间"
};

const onCreateTable = async (data: any) => {
  await fetchTableList(); // 刷新表列表
  showCreateTable.value = false;
  editTableData.value = null; // 重置编辑数据

  // 自动选中刚刚编辑/新增的表，并刷新右侧详情
  if (data && data.id) {
    currentTable.value = data.id.toString();
    // 递归查找treeData，找到对应节点并触发handleNodeClick
    const findNode = (nodes: any[]): any => {
      for (const node of nodes) {
        if (node.children) {
          for (const child of node.children) {
            if (child.id === data.id) {
              handleNodeClick(child);
              return true;
            }
          }
          if (findNode(node.children)) return true;
        }
      }
      return false;
    };
    findNode(treeData.value);
  }
};

const removeField = (index: number) => {
  fieldList.value.splice(index, 1);
};

const defaultProps = {
  children: "children",
  label: "label"
};

const handleNodeClick = (data: any) => {
  if (data.tableInfo) {
    // 点击的是表节点
    currentTable.value = data.id;
    tableInfo.value = {
      tableName: data.tableInfo.tableName,
      comment: data.tableInfo.tableCn,
      domain: data.domainName
    };
    // 更新字段列表
    fieldList.value =
      data.tableInfo.columns?.map((column: any) => ({
        fieldName: column.name,
        fieldType: getFieldTypeString(column.fieldType),
        length: column.fieldLength.toString(),
        decimal: column.sacle.toString(),
        isNull: column.nullable === 1,
        isPrimary: column.pk === 1,
        comment: column.comment
      })) || [];
  }
};

// 添加字段类型转换函数
const getFieldTypeString = (typeNumber: number): string => {
  const typeMap: { [key: number]: string } = {
    1: "float", // 浮点数
    2: "string", // 字符串
    3: "date", // 日期
    4: "boolean", // 布尔值
    5: "integer", // 整数
    6: "bigint", // 大整数
    7: "serializable", // 可序列化
    8: "binary", // 二进制
    9: "timestamp", // 时间戳
    10: "time" // 时间
  };
  return typeMap[typeNumber] || "string";
};

// 数据域详情
const domainDetail = ref<DomainDetail | null>(null);

// 获取数据域详情
const fetchDomainDetail = async () => {
  if (!domainId.value) {
    ElMessage.error("缺少必要的参数：id");
    return;
  }

  try {
    const { code, data, msg } = await getDomainDetail(domainId.value);
    if (code === 0 && data) {
      domainDetail.value = data;
      // 如果是新建模式，设置初始数据
      if (mode.value === "create") {
        tableInfo.value = {
          ...tableInfo.value,
          domain: data.name || ""
        };
      }
    } else {
      ElMessage.error(msg || "获取数据域详情失败");
    }
  } catch (error) {
    console.error("获取数据域详情失败:", error);
    ElMessage.error("获取数据域详情失败");
  }
};

// 获取表列表
const fetchTableList = async () => {
  // 根据mode判断使用哪个参数
  const params =
    mode.value === "manage"
      ? { layerId: layerId.value }
      : { domainId: domainId.value };

  if (!params.layerId && !params.domainId) {
    ElMessage.error("缺少必要的参数");
    return;
  }

  try {
    const { code, data, msg } = await getTableList(params);
    if (code === 0 && data) {
      // 设置数据域选项
      domainOptions.value = data.layeringDomainTableInfos.map(domain => ({
        label: domain.domainName,
        value: domain.domainId
      }));

      if (mode.value === "manage") {
        // 管理模式：显示所有数据域的表
        treeData.value = data.layeringDomainTableInfos.map(domain => ({
          id: domain.domainId.toString(),
          label: domain.domainName,
          children: domain.tableInfos.map(table => ({
            id: table.id,
            label: table.tableInfo.tableName,
            tableInfo: table.tableInfo,
            domainName: domain.domainName // 保存域名，供表详情使用
          }))
        }));
      } else {
        // 查看模式：只显示当前数据域的表
        const currentDomain = data.layeringDomainTableInfos.find(
          domain => domain.domainId.toString() === domainId.value
        );
        if (currentDomain) {
          treeData.value = [
            {
              id: currentDomain.domainId.toString(),
              label: currentDomain.domainName,
              children: currentDomain.tableInfos.map(table => ({
                id: table.id,
                label: table.tableInfo.tableName,
                tableInfo: table.tableInfo,
                domainName: currentDomain.domainName
              }))
            }
          ];
        }
      }
    } else {
      ElMessage.error(msg || "获取表列表失败");
    }
  } catch (error) {
    console.error("获取表列表失败:", error);
    ElMessage.error("获取表列表失败");
  }
};

// 组件激活时获取数据
onActivated(async () => {
  // 只有在view模式下才需要获取数据域详情
  if (mode.value === "view") {
    await fetchDomainDetail();
  }
  await fetchTableList();
});

// 组件首次挂载时也获取数据
onMounted(async () => {
  // 只有在view模式下才需要获取数据域详情
  if (mode.value === "view") {
    await fetchDomainDetail();
  }
  await fetchTableList();
});

// 组件失活时清理状态
onDeactivated(() => {
  // 清理组件状态
  currentTable.value = null;
  tableInfo.value = null;
  fieldList.value = [];
  domainOptions.value = [];
  treeData.value = [];
});

// 处理编辑表
const handleEditTable = () => {
  if (!currentTable.value) {
    ElMessage.warning("请先选择要编辑的表");
    return;
  }

  // 找到当前选中的表数据
  const findTableData = (nodes: any[]): any => {
    for (const node of nodes) {
      if (node.children) {
        for (const child of node.children) {
          if (child.id === currentTable.value) {
            return {
              id: child.id,
              tableName: child.tableInfo.tableName,
              comment: child.tableInfo.tableCn,
              domain: Number(node.id), // 修正为数字类型
              fields: child.tableInfo.columns.map((col: any) => ({
                fieldName: col.name,
                fieldType: col.fieldType,
                length: col.fieldLength.toString(),
                decimal: col.sacle.toString(),
                isNull: col.nullable === 1,
                isPrimary: col.pk === 1,
                comment: col.comment
              }))
            };
          }
        }
        const found = findTableData(node.children);
        if (found) return found;
      }
    }
    return null;
  };

  const tableData = findTableData(treeData.value);
  if (tableData) {
    showCreateTable.value = true;
    // 将表数据传递给对话框组件
    editTableData.value = tableData;
  }
};

// 处理删除表
const handleDeleteTable = async () => {
  if (!currentTable.value) {
    ElMessage.warning("请先选择要删除的表");
    return;
  }

  try {
    await ElMessageBox.confirm("确定要删除该表吗？删除后将无法恢复。", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    });

    // 确保 tableId 是数字类型
    const tableId = parseInt(currentTable.value);
    if (isNaN(tableId)) {
      ElMessage.error("无效的表ID");
      return;
    }

    await deleteTable([tableId]);
    ElMessage.success("删除成功");

    // 重置当前选中的表
    currentTable.value = null;
    tableInfo.value = {
      tableName: "",
      comment: "",
      domain: ""
    };
    fieldList.value = [];

    // 刷新表列表
    await fetchTableList();
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除失败:", error);
      ElMessage.error("删除失败");
    }
  }
};
</script>

<style lang="scss" scoped>
.table-detail-container {
  height: 98%;
  display: flex;
  flex-direction: column;
  overflow: hidden; // 防止容器本身滚动

  .page-header {
    margin-bottom: 20px;
    h2 {
      margin: 0;
      font-size: 20px;
      font-weight: 500;
    }
  }

  .page-content {
    flex: 1;
    display: flex;
    gap: 20px;
    min-height: 0;
    overflow: hidden; // 防止内容溢出

    .table-list {
      width: 250px;
      background: #fff;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .header {
        padding: 12px 15px;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #eee;

        .action-buttons {
          display: flex;
          gap: 8px; // 减小按钮之间的间距

          .el-button {
            display: inline-flex;
            align-items: center;
            height: 32px; // 统一按钮高度

            .icon-btn {
              font-size: 16px; // 增大图标尺寸
              margin-right: 4px; // 调整图标和文字的间距
            }
          }
        }
      }

      .list {
        flex: 1;
        overflow-y: auto;
        padding: 10px;

        :deep(.el-tree-node__content) {
          height: 32px;
        }

        :deep(.el-tree-node__children .el-tree-node__content) {
          padding-left: 8px;
        }

        .custom-tree-node {
          flex: 1;
          display: flex;
          align-items: center;
          font-size: 14px;
          padding: 4px 8px;
          border-radius: 4px;

          &.active {
            background-color: #ecf5ff;
            color: #409eff;
          }
        }
      }
    }

    .table-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-width: 0;
      overflow: hidden; // 防止内容溢出

      .box-card {
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden; // 防止内容溢出
      }

      .table-actions {
        display: flex;
        justify-content: flex-end;
        gap: 12px;
        margin-bottom: 20px;
        padding: 0 8px;
      }

      .info-section,
      .field-section {
        background: #eef3fb;
        border-radius: 10px;
        padding: 24px;
        margin-bottom: 20px;

        .section-title {
          display: flex;
          align-items: center;
          font-size: 18px;
          font-weight: bold;
          color: #222;
          margin-bottom: 16px;

          &::before {
            content: "";
            display: inline-block;
            width: 4px;
            height: 22px;
            background: #409eff;
            border-radius: 2px;
            margin-right: 8px;
          }
        }
      }

      .info-section {
        .table-info-row {
          display: flex;
          gap: 32px;

          .info-item {
            flex: 1;
            display: flex;
            align-items: center;

            .label {
              color: #606266;
              margin-right: 8px;
              font-size: 14px;
            }

            .value {
              color: #333;
              font-size: 14px;
            }
          }
        }
      }

      .field-section {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden; // 防止内容溢出

        .el-table {
          flex: 1;
          overflow-y: auto;
        }
      }
    }
  }
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background: #f5f7fa;
  border-radius: 4px;
}
</style>
