<template>
  <el-dialog
    v-model="visible"
    :title="props.editData ? '编辑表' : '新建表'"
    width="70%"
    :close-on-click-modal="false"
    @close="handleCancel"
  >
    <div class="dialog-body">
      <!-- 表信息容器 -->
      <div class="section-box section-box-info">
        <div class="section-title">表信息</div>
        <div class="table-info-row">
          <div class="form-label"><span class="required">*</span>表名</div>
          <el-input
            v-model="form.tableName"
            placeholder="表名"
            class="form-input"
          />
          <div class="form-label"><span class="required">*</span>注释</div>
          <el-input
            v-model="form.comment"
            placeholder="中文名称"
            class="form-input"
          />
          <div class="form-label"><span class="required">*</span>数据域</div>
          <el-select
            v-model="form.domain"
            placeholder="请选择领域"
            class="form-select"
          >
            <el-option
              v-for="item in domainOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
      </div>

      <!-- 字段管理容器 -->
      <div class="section-box section-box-field">
        <div class="section-title">字段管理</div>
        <div class="add-field-row">
          <el-link type="primary" @click="addField">添加字段</el-link>
        </div>
        <el-table
          :data="form.fields"
          class="full-width-table"
          style="width: 100%"
        >
          <el-table-column prop="fieldName" label="字段名称">
            <template #default="scope">
              <el-input v-model="scope.row.fieldName" placeholder="字段名称" />
            </template>
          </el-table-column>
          <el-table-column prop="fieldType" label="字段类型">
            <template #default="scope">
              <el-select
                v-model="scope.row.fieldType"
                placeholder="字段类型"
                style="width: 100%"
              >
                <el-option
                  v-for="item in fieldTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="length" label="长度">
            <template #default="scope">
              <el-input v-model="scope.row.length" placeholder="长度" />
            </template>
          </el-table-column>
          <el-table-column prop="decimal" label="小数位数">
            <template #default="scope">
              <el-input v-model="scope.row.decimal" placeholder="小数位数" />
            </template>
          </el-table-column>
          <el-table-column prop="isNull" label="可空">
            <template #default="scope">
              <el-checkbox v-model="scope.row.isNull" />
            </template>
          </el-table-column>
          <el-table-column prop="isPrimary" label="主键">
            <template #default="scope">
              <el-checkbox v-model="scope.row.isPrimary" />
            </template>
          </el-table-column>
          <el-table-column prop="comment" label="注释">
            <template #default="scope">
              <el-input v-model="scope.row.comment" placeholder="注释" />
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <el-link type="danger" @click="removeField(scope.$index)"
                >删除</el-link
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, watch } from "vue";
import { ElMessage } from "element-plus";
import { saveTable, updateTable } from "@/api/data-warehouse";
import type { SaveTableRequest, TableColumn } from "@/api/data-warehouse";

interface DomainOption {
  label: string;
  value: number;
}

const props = defineProps({
  modelValue: Boolean,
  domainOptions: {
    type: Array as () => DomainOption[],
    required: true
  },
  editData: {
    type: Object as () => {
      id: number;
      tableName: string;
      comment: string;
      domain: number;
      fields: Array<{
        fieldName: string;
        fieldType: string;
        length: string;
        decimal: string;
        isNull: boolean;
        isPrimary: boolean;
        comment: string;
      }>;
    } | null,
    default: null
  }
});
const emit = defineEmits(["update:modelValue", "confirm"]);

const visible = ref(props.modelValue);
watch(
  () => props.modelValue,
  v => (visible.value = v)
);
watch(visible, v => emit("update:modelValue", v));

// 修改表单结构以匹配API要求
const form = ref({
  id: 0,
  tableName: "",
  comment: "",
  domain: undefined as number | undefined,
  fields: [
    {
      fieldName: "",
      fieldType: "string",
      length: "",
      decimal: "",
      isNull: true,
      isPrimary: false,
      comment: ""
    }
  ]
});

// 字段类型映射
const fieldTypeMap = {
  VARCHAR: 1,
  INT: 2,
  BIGINT: 3,
  DECIMAL: 4,
  DATETIME: 5,
  DATE: 6,
  TIMESTAMP: 7,
  TEXT: 8,
  BOOLEAN: 9
} as const;

// 反向映射，用于将数字类型转换为字符串类型
const reverseFieldTypeMap: Record<number, string> = Object.entries(
  fieldTypeMap
).reduce((acc, [key, value]) => ({ ...acc, [value]: key }), {});

// 将数字类型转换为字符串类型
const getFieldTypeString = (type: number): string => {
  return reverseFieldTypeMap[type] || "VARCHAR";
};

const fieldTypeOptions = [
  { label: "浮点数 (Float)", value: "float" },
  { label: "字符串 (String)", value: "string" },
  { label: "日期 (Date)", value: "date" },
  { label: "布尔值 (Boolean)", value: "boolean" },
  { label: "整数 (Integer)", value: "integer" },
  { label: "大整数 (BigInt)", value: "bigint" },
  { label: "可序列化 (Serializable)", value: "serializable" },
  { label: "二进制 (Binary)", value: "binary" },
  { label: "时间戳 (Timestamp)", value: "timestamp" },
  { label: "时间 (Time)", value: "time" }
];

const addField = () => {
  form.value.fields.push({
    fieldName: "",
    fieldType: "string",
    length: "",
    decimal: "",
    isNull: true,
    isPrimary: false,
    comment: ""
  });
};

const removeField = (index: number) => {
  form.value.fields.splice(index, 1);
};

const handleCancel = () => {
  visible.value = false;
  // 重置表单
  form.value = {
    id: 0,
    tableName: "",
    comment: "",
    domain: undefined,
    fields: [
      {
        fieldName: "",
        fieldType: "string",
        length: "",
        decimal: "",
        isNull: true,
        isPrimary: false,
        comment: ""
      }
    ]
  };
};

const validateForm = () => {
  if (!form.value.tableName) {
    ElMessage.error("请输入表名");
    return false;
  }
  if (!form.value.comment) {
    ElMessage.error("请输入表注释");
    return false;
  }
  if (!form.value.domain) {
    ElMessage.error("请选择数据域");
    return false;
  }
  if (!form.value.fields.length) {
    ElMessage.error("请至少添加一个字段");
    return false;
  }

  // 验证字段信息
  for (const field of form.value.fields) {
    if (!field.fieldName) {
      ElMessage.error("请输入字段名称");
      return false;
    }
    if (!field.comment) {
      ElMessage.error("请输入字段注释");
      return false;
    }
  }

  return true;
};

// 监听editData变化
watch(
  () => props.editData,
  newVal => {
    if (newVal) {
      form.value = {
        id: newVal.id,
        tableName: newVal.tableName,
        comment: newVal.comment,
        domain: newVal.domain,
        fields: newVal.fields.map(field => ({
          ...field,
          fieldType: getFieldTypeString(Number(field.fieldType)),
          length: field.length.toString(),
          decimal: field.decimal.toString()
        }))
      };
    } else {
      // 重置表单
      form.value = {
        id: 0,
        tableName: "",
        comment: "",
        domain: undefined,
        fields: [
          {
            fieldName: "",
            fieldType: "string",
            length: "",
            decimal: "",
            isNull: true,
            isPrimary: false,
            comment: ""
          }
        ]
      };
    }
  },
  { immediate: true }
);

const handleConfirm = async () => {
  if (!validateForm()) {
    return;
  }

  try {
    // 转换数据格式
    const requestData: SaveTableRequest & { id?: number } = {
      id: form.value.id,
      domainId: form.value.domain!,
      tableInfo: {
        tableName: form.value.tableName,
        tableCn: form.value.comment,
        columns: form.value.fields.map(field => ({
          name: field.fieldName,
          comment: field.comment,
          fieldType: fieldTypeMap[field.fieldType as keyof typeof fieldTypeMap],
          fieldLength: parseInt(field.length) || 0,
          sacle: parseInt(field.decimal) || 0,
          nullable: field.isNull ? 1 : 0,
          pk: field.isPrimary ? 1 : 0
        }))
      }
    };

    if (form.value.id) {
      // 编辑模式
      await updateTable(requestData as SaveTableRequest & { id: number });
      ElMessage.success("更新表成功");
    } else {
      // 新增模式
      await saveTable(requestData);
      ElMessage.success("创建表成功");
    }

    emit("confirm", requestData);
    visible.value = false;
    handleCancel(); // 重置表单
  } catch (error) {
    console.error(form.value.id ? "更新表失败:" : "创建表失败:", error);
    ElMessage.error(form.value.id ? "更新表失败" : "创建表失败");
  }
};
</script>

<script lang="ts">
export default {
  name: "CreateTableDialog"
};
</script>

<style scoped>
.dialog-body {
  background: #fff;
  border-radius: 10px;
  padding: 24px 24px 12px 24px;
}
.section-box {
  background: #eef3fb !important;
  border-radius: 10px;
  padding: 24px 24px 12px 24px;
  margin-bottom: 32px;
}
.section-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
  color: #222;
  margin-bottom: 16px;
}
.section-title::before {
  content: "";
  display: inline-block;
  width: 4px;
  height: 22px;
  background: #409eff;
  border-radius: 2px;
  margin-right: 8px;
}
.table-info-row {
  display: flex;
  align-items: center;
  gap: 18px;
  margin-bottom: 10px;
}
.form-label {
  min-width: 60px;
  text-align: right;
  font-size: 15px;
  color: #333;
}
.form-input {
  width: 200px;
}
.form-select {
  width: 160px;
}
.required {
  color: #f56c6c;
  margin-right: 2px;
}
.add-field-row {
  margin-bottom: 6px;
}
.full-width-table {
  width: 100% !important;
  min-width: 100% !important;
}
:deep(.el-table__body-wrapper) {
  width: 100% !important;
}
</style>
