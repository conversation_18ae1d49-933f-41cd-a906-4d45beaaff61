import { defineComponent, ref, watch } from "vue";
import {
  ElDialog,
  ElInput,
  ElSelect,
  ElOption,
  ElButton,
  ElTable,
  ElTableColumn,
  ElCheckbox,
  ElMessage
} from "element-plus";
import { saveTable, updateTable } from "@/api/data-warehouse";
import type { SaveTableRequest } from "@/api/data-warehouse";

interface DomainOption {
  label: string;
  value: number;
}

interface FormField {
  fieldName: string;
  fieldType: string;
  length: string;
  decimal: string;
  isNull: boolean;
  isPrimary: boolean;
  comment: string;
}

const fieldTypeMap = {
  VARCHAR: 1,
  INT: 2,
  BIGINT: 3,
  DECIMAL: 4,
  DATETIME: 5,
  DATE: 6,
  TIMESTAMP: 7,
  TEXT: 8,
  BOOLEAN: 9
} as const;

const reverseFieldTypeMap: Record<number, string> = Object.entries(
  fieldTypeMap
).reduce((acc, [key, value]) => ({ ...acc, [value]: key }), {});

const fieldTypeOptions = [
  { label: "浮点数 (Float)", value: "float" },
  { label: "字符串 (String)", value: "string" },
  { label: "日期 (Date)", value: "date" },
  { label: "布尔值 (Boolean)", value: "boolean" },
  { label: "整数 (Integer)", value: "integer" },
  { label: "大整数 (BigInt)", value: "bigint" },
  { label: "可序列化 (Serializable)", value: "serializable" },
  { label: "二进制 (Binary)", value: "binary" },
  { label: "时间戳 (Timestamp)", value: "timestamp" },
  { label: "时间 (Time)", value: "time" }
];

export default defineComponent({
  name: "CreateTableDialog",

  props: {
    modelValue: {
      type: Boolean,
      required: true
    },
    domainOptions: {
      type: Array as () => DomainOption[],
      required: true
    },
    editData: {
      type: Object as () => {
        id: number;
        tableName: string;
        comment: string;
        domain: number;
        fields: FormField[];
      } | null,
      default: null
    }
  },

  emits: ["update:modelValue", "confirm"],

  setup(props, { emit }) {
    const visible = ref(props.modelValue);

    const form = ref({
      id: 0,
      tableName: "",
      comment: "",
      domain: undefined as number | undefined,
      fields: [
        {
          fieldName: "",
          fieldType: "string",
          length: "",
          decimal: "",
          isNull: true,
          isPrimary: false,
          comment: ""
        }
      ]
    });

    watch(
      () => props.modelValue,
      val => (visible.value = val)
    );

    watch(visible, val => emit("update:modelValue", val));

    watch(
      () => props.editData,
      newVal => {
        if (newVal) {
          form.value = {
            id: newVal.id,
            tableName: newVal.tableName,
            comment: newVal.comment,
            domain: newVal.domain,
            fields: newVal.fields.map(field => ({
              ...field,
              fieldType: reverseFieldTypeMap[Number(field.fieldType)],
              length: field.length.toString(),
              decimal: field.decimal.toString()
            }))
          };
        } else {
          form.value = {
            id: 0,
            tableName: "",
            comment: "",
            domain: undefined,
            fields: [
              {
                fieldName: "",
                fieldType: "string",
                length: "",
                decimal: "",
                isNull: true,
                isPrimary: false,
                comment: ""
              }
            ]
          };
        }
      },
      { immediate: true }
    );

    const addField = () => {
      form.value.fields.push({
        fieldName: "",
        fieldType: "string",
        length: "",
        decimal: "",
        isNull: true,
        isPrimary: false,
        comment: ""
      });
    };

    const removeField = (index: number) => {
      form.value.fields.splice(index, 1);
    };

    const validateForm = () => {
      if (!form.value.tableName) {
        ElMessage.error("请输入表名");
        return false;
      }
      if (!form.value.comment) {
        ElMessage.error("请输入表注释");
        return false;
      }
      if (!form.value.domain) {
        ElMessage.error("请选择数据域");
        return false;
      }
      if (!form.value.fields.length) {
        ElMessage.error("请至少添加一个字段");
        return false;
      }

      for (const field of form.value.fields) {
        if (!field.fieldName) {
          ElMessage.error("请输入字段名称");
          return false;
        }
        if (!field.comment) {
          ElMessage.error("请输入字段注释");
          return false;
        }
      }

      return true;
    };

    const handleConfirm = async () => {
      if (!validateForm()) {
        return;
      }

      try {
        const requestData: SaveTableRequest & { id?: number } = {
          id: form.value.id,
          domainId: form.value.domain!,
          tableInfo: {
            tableName: form.value.tableName,
            tableCn: form.value.comment,
            columns: form.value.fields.map(field => ({
              name: field.fieldName,
              comment: field.comment,
              fieldType:
                fieldTypeMap[field.fieldType as keyof typeof fieldTypeMap],
              fieldLength: parseInt(field.length) || 0,
              sacle: parseInt(field.decimal) || 0,
              nullable: field.isNull ? 1 : 0,
              pk: field.isPrimary ? 1 : 0
            }))
          }
        };

        if (form.value.id) {
          await updateTable(requestData as SaveTableRequest & { id: number });
          ElMessage.success("更新表成功");
        } else {
          await saveTable(requestData);
          ElMessage.success("创建表成功");
        }

        emit("confirm", requestData);
        visible.value = false;
      } catch (error) {
        console.error(form.value.id ? "更新表失败:" : "创建表失败:", error);
        ElMessage.error(form.value.id ? "更新表失败" : "创建表失败");
      }
    };

    const handleCancel = () => {
      visible.value = false;
      form.value = {
        id: 0,
        tableName: "",
        comment: "",
        domain: undefined,
        fields: [
          {
            fieldName: "",
            fieldType: "string",
            length: "",
            decimal: "",
            isNull: true,
            isPrimary: false,
            comment: ""
          }
        ]
      };
    };

    return () => (
      <ElDialog
        v-model={[visible.value, "modelValue"]}
        title={props.editData ? "编辑表" : "新建表"}
        width="70%"
        onClose={handleCancel}
      >
        <div class="dialog-body">
          <div class="section-box section-box-info">
            <div class="section-title">表信息</div>
            <div class="table-info-row">
              <div class="form-label">
                <span class="required">*</span>表名
              </div>
              <ElInput
                v-model={[form.value.tableName, "value"]}
                placeholder="表名"
                class="form-input"
              />
              <div class="form-label">
                <span class="required">*</span>注释
              </div>
              <ElInput
                v-model={[form.value.comment, "value"]}
                placeholder="中文名称"
                class="form-input"
              />
              <div class="form-label">
                <span class="required">*</span>数据域
              </div>
              <ElSelect
                v-model={[form.value.domain, "value"]}
                placeholder="请选择领域"
                class="form-select"
              >
                {props.domainOptions.map(option => (
                  <ElOption
                    key={option.value}
                    label={option.label}
                    value={option.value}
                  />
                ))}
              </ElSelect>
            </div>
          </div>

          <div class="section-box section-box-field">
            <div class="section-title">字段管理</div>
            <div class="add-field-row">
              <ElButton type="primary" link onClick={addField}>
                添加字段
              </ElButton>
            </div>
            <ElTable data={form.value.fields} class="full-width-table">
              <ElTableColumn label="字段名称">
                {{
                  default: ({ $index }: { $index: number }) => (
                    <ElInput
                      v-model={[form.value.fields[$index].fieldName, "value"]}
                      placeholder="字段名称"
                    />
                  )
                }}
              </ElTableColumn>
              <ElTableColumn label="字段类型">
                {{
                  default: ({ $index }: { $index: number }) => (
                    <ElSelect
                      v-model={[form.value.fields[$index].fieldType, "value"]}
                      placeholder="字段类型"
                      style={{ width: "100%" }}
                    >
                      {fieldTypeOptions.map(option => (
                        <ElOption
                          key={option.value}
                          label={option.label}
                          value={option.value}
                        />
                      ))}
                    </ElSelect>
                  )
                }}
              </ElTableColumn>
              <ElTableColumn label="长度">
                {{
                  default: ({ $index }: { $index: number }) => (
                    <ElInput
                      v-model={[form.value.fields[$index].length, "value"]}
                      placeholder="长度"
                    />
                  )
                }}
              </ElTableColumn>
              <ElTableColumn label="小数位数">
                {{
                  default: ({ $index }: { $index: number }) => (
                    <ElInput
                      v-model={[form.value.fields[$index].decimal, "value"]}
                      placeholder="小数位数"
                    />
                  )
                }}
              </ElTableColumn>
              <ElTableColumn label="可空">
                {{
                  default: ({ $index }: { $index: number }) => (
                    <ElCheckbox
                      v-model={[form.value.fields[$index].isNull, "modelValue"]}
                    />
                  )
                }}
              </ElTableColumn>
              <ElTableColumn label="主键">
                {{
                  default: ({ $index }: { $index: number }) => (
                    <ElCheckbox
                      v-model={[
                        form.value.fields[$index].isPrimary,
                        "modelValue"
                      ]}
                    />
                  )
                }}
              </ElTableColumn>
              <ElTableColumn label="注释">
                {{
                  default: ({ $index }: { $index: number }) => (
                    <ElInput
                      v-model={[form.value.fields[$index].comment, "value"]}
                      placeholder="注释"
                    />
                  )
                }}
              </ElTableColumn>
              <ElTableColumn label="操作">
                {{
                  default: ({ $index }: { $index: number }) => (
                    <ElButton
                      type="danger"
                      link
                      onClick={() => removeField($index)}
                    >
                      删除
                    </ElButton>
                  )
                }}
              </ElTableColumn>
            </ElTable>
          </div>
        </div>
        <template v-slots:footer>
          <ElButton onClick={handleCancel}>取消</ElButton>
          <ElButton type="primary" onClick={handleConfirm}>
            确定
          </ElButton>
        </template>
      </ElDialog>
    );
  }
});

// 添加样式
const styles = `
.dialog-body {
  background: #fff;
  border-radius: 10px;
  padding: 24px 24px 12px 24px;
}

.section-box {
  background: #eef3fb !important;
  border-radius: 10px;
  padding: 24px 24px 12px 24px;
  margin-bottom: 32px;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
  color: #222;
  margin-bottom: 16px;
}

.section-title::before {
  content: "";
  display: inline-block;
  width: 4px;
  height: 22px;
  background: #409eff;
  border-radius: 2px;
  margin-right: 8px;
}

.table-info-row {
  display: flex;
  align-items: center;
  gap: 18px;
  margin-bottom: 10px;
}

.form-label {
  min-width: 60px;
  text-align: right;
  font-size: 15px;
  color: #333;
}

.form-input {
  width: 200px;
}

.form-select {
  width: 160px;
}

.required {
  color: #f56c6c;
  margin-right: 2px;
}

.add-field-row {
  margin-bottom: 6px;
}

.full-width-table {
  width: 100% !important;
  min-width: 100% !important;
}
`;

// 将样式添加到文档中
const styleSheet = document.createElement("style");
styleSheet.type = "text/css";
styleSheet.innerText = styles;
document.head.appendChild(styleSheet);
