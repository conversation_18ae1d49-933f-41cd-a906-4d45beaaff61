<template>
  <div class="layering-container">
    <!-- 搜索框和新增按钮 -->
    <div class="header-container">
      <el-button type="primary" :icon="Plus" @click="handleAdd()">
        新增分层
      </el-button>
      <el-input
        v-model="searchText"
        placeholder="请输入数据分层名称"
        :suffix-icon="Search"
        class="search-input"
      />
    </div>

    <!-- 遍历渲染各层 -->
    <div v-for="layer in layerList" :key="layer.id" class="layer-section">
      <div class="layer-header">
        <div class="layer-title">{{ layer.name }}</div>
        <div class="layer-actions">
          <el-link
            type="primary"
            class="action-link"
            @click="handleTableManage(layer)"
          >
            <el-icon><List /></el-icon>
            表管理
          </el-link>
          <el-link
            type="primary"
            class="action-link"
            @click="handleEditLayer(layer)"
          >
            <el-icon><Edit /></el-icon>
            编辑层
          </el-link>
          <el-link
            type="danger"
            class="action-link"
            @click="handleDeleteLayer(layer)"
          >
            <el-icon><Delete /></el-icon>
            删除层
          </el-link>
          <el-link
            type="primary"
            class="action-link"
            @click="handleAdd(layer.shortName, layer)"
          >
            <el-icon><Plus /></el-icon>
            新增数据域
          </el-link>
        </div>
      </div>
      <div class="layer-content">
        <layer-card
          v-for="domain in layer.domains"
          :key="domain.id"
          :title="domain.name"
          :description="domain.description || ''"
          :domain-id="domain.id"
          :owner="domain.ownPersonName"
          :layer-name="layer.name"
          @edit-layer="handleEditDomain(domain, layer)"
          @refresh="fetchLayerList"
        />
      </div>
    </div>

    <!-- 新增/编辑分层弹窗 -->
    <add-layer-dialog
      v-model:visible="dialogVisible"
      :title="dialogTitle"
      :current-layer="currentLayer"
      @submit="handleSubmit"
    />

    <!-- 新增/编辑数据域弹窗 -->
    <add-domain-dialog
      v-model:visible="domainDialogVisible"
      :title="dialogTitle"
      :parent-layer="parentLayer"
      :current-domain="currentDomain"
      @submit="handleSubmit"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { debounce } from "lodash-es";
import { useRouter, useRoute } from "vue-router";

import { Search, Plus, Edit, Delete, List } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
import LayerCard from "./components/LayerCard.vue";
import AddLayerDialog from "./components/AddLayerDialog.vue";
import AddDomainDialog from "./components/AddDomainDialog.vue";
import {
  getLayerList,
  saveLayer,
  saveDomain,
  deleteLayer,
  updateLayer,
  updateDomain,
  addMenuLayer
} from "@/api/data-warehouse";
import { getMenuNav } from "@/api/user";
import type { Layer, AddMenuLayerParams } from "@/api/data-warehouse";

const searchText = ref("");
const dialogVisible = ref(false);
const domainDialogVisible = ref(false);
const dialogTitle = ref("");
const currentLayer = ref(null);
const parentLayer = ref(null);
const currentDomain = ref(null);
const layerList = ref<Layer[]>([]);
const router = useRouter();
const route = useRoute();

interface LayerApiResponse {
  code: number;
  msg: string;
  data: {
    layerId: number;
    layerName: string;
    domainId?: number;
    domainName?: string;
  } | null;
}

// 获取数据列表
const fetchLayerList = async () => {
  try {
    const fullUrl = new URL(window.location.href);
    const hash = fullUrl.hash;
    const queryString = hash.split("?")[1];
    const paramsObj = new URLSearchParams(queryString);
    const layerId = paramsObj.get("layerId");
    const params: { layerId?: string | number } = {};

    if (layerId && !Array.isArray(layerId)) {
      params.layerId = layerId;
    }

    const { data, code, msg } = await getLayerList(params);
    if (code === 0) {
      // 确保数据完整性
      layerList.value = data.map(layer => ({
        ...layer,
        domains:
          layer.domains?.map(domain => ({
            ...domain,
            id: domain.id || undefined // 确保id存在或设为undefined
          })) || []
      }));
    } else {
      ElMessage.error(msg || "获取数据失败");
    }
  } catch (error) {
    console.error("获取数据失败:", error);
    ElMessage.error("获取数据失败");
  }
};

const handleAdd = (category?: string, layer?: any) => {
  dialogTitle.value = category ? "新增数据域" : "新增分层";

  if (category && layer) {
    // 新增数据域
    parentLayer.value = {
      id: layer.id,
      name: layer.name
    };
    currentDomain.value = null;
    domainDialogVisible.value = true;
  } else {
    // 新增分层
    parentLayer.value = null;
    currentLayer.value = null;
    dialogVisible.value = true;
  }
};

const handleSubmit = async (formData: any) => {
  try {
    if (domainDialogVisible.value) {
      // 数据域操作
      const api = formData.id ? updateDomain : saveDomain;
      const result = await api({
        ...formData,
        layeringType: formData.category
      });
      const response = result as unknown as LayerApiResponse;

      // 如果是新增数据域，添加菜单层级
      if (!formData.id && response.code === 0 && response.data) {
        await addMenuLayer({
          layeringId: response.data.layerId,
          layeringName: response.data.layerName,
          domainId: response.data.domainId,
          domainName: response.data.domainName,
          menuType: 2, // 数据域类型为2
          operation: 1 // 新增操作为1
        });

        // 刷新菜单导航
        await getMenuNav();
      }

      ElMessage.success(formData.id ? "更新数据域成功" : "新增数据域成功");
      domainDialogVisible.value = false;
    } else {
      // 分层操作
      const api = formData.id ? updateLayer : saveLayer;
      const result = await api(formData);
      const response = result as unknown as LayerApiResponse;

      // 如果是新增分层，添加菜单层级
      if (!formData.id && response.code === 0 && response.data) {
        await addMenuLayer({
          layeringId: response.data.layerId,
          layeringName: response.data.layerName,
          domainId: response.data.domainId,
          domainName: response.data.domainName,
          menuType: 1, // 分层类型为1
          operation: 1 // 新增操作为1
        });

        // 刷新菜单导航
        await getMenuNav();
      }

      ElMessage.success(formData.id ? "编辑分层成功" : "新增分层成功");
      dialogVisible.value = false;
    }

    // 重置状态
    currentLayer.value = null;
    parentLayer.value = null;
    currentDomain.value = null;

    // 刷新列表
    await fetchLayerList();
  } catch (error) {
    console.error("操作失败:", error);
    ElMessage.error("操作失败");
  }
};

const handleEditLayer = (layer: Layer) => {
  dialogTitle.value = "编辑分层";
  currentLayer.value = layer;
  dialogVisible.value = true;
};

const handleEditDomain = (domain: any, layer: Layer) => {
  dialogTitle.value = "编辑数据域";
  currentDomain.value = domain;
  parentLayer.value = {
    id: layer.id,
    name: layer.name
  };
  domainDialogVisible.value = true;
};

const handleDeleteLayer = async (layer: Layer) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除数仓层"${layer.name}"吗？删除后将无法恢复。`,
      "警告",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    );

    await deleteLayer([layer.id]);

    // 删除对应的菜单层级
    await addMenuLayer({
      layeringId: layer.id,
      layeringName: layer.name,
      menuType: 1, // 分层类型为1
      operation: 2 // 删除操作为2
    });

    // 刷新菜单导航
    await getMenuNav();

    ElMessage.success("删除成功");
    await fetchLayerList();
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除失败:", error);
      ElMessage.error("删除失败");
    }
  }
};

const handleTableManage = (layer: Layer) => {
  router.push(`/data-warehouse/table-detail?mode=manage&layerId=${layer.id}`);
};

// 组件挂载时获取数据
onMounted(() => {
  console.log("route.query", route.query);
  fetchLayerList();
});
</script>

<style scoped>
.layering-container {
  width: 100%;
}

.header-container {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.search-input {
  width: 300px;
}

.layer-section {
  margin-bottom: 24px;
}

.layer-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding: 0 8px;
}

.layer-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.layer-title::before {
  content: "";
  display: inline-block;
  width: 4px;
  height: 20px;
  background: #409eff;
  border-radius: 2px;
}

.layer-content {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.layer-actions {
  display: flex;
  align-items: center;
  gap: 4px;
}

.action-link {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  margin-left: 8px;
  font-size: 14px;
  transition: color 0.2s;
}

.action-link :deep(.el-icon) {
  font-size: 16px;
}

.action-link.el-link--primary:hover {
  color: var(--el-color-primary) !important;
}

.action-link.el-link--danger:hover {
  color: var(--el-color-danger) !important;
}

.add-button {
  padding: 4px 8px;
  font-size: 14px;
  height: auto;
  display: flex;
  align-items: center;
}

.add-button:hover {
  background-color: transparent;
}
</style>
