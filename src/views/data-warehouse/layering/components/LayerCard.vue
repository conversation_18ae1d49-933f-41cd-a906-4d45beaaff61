<template>
  <el-card class="layer-card">
    <div class="card-content">
      <div class="card-header">
        <div class="card-icon">
          <el-icon><DataLine /></el-icon>
        </div>
        <div class="card-actions">
          <el-link
            type="primary"
            class="action-link"
            @click="handleCreateTable"
          >
            <el-icon><View /></el-icon>
            查看表
          </el-link>
          <el-link type="primary" class="action-link">
            <el-icon><Setting /></el-icon>
            设置
          </el-link>
          <el-link
            type="primary"
            class="action-link"
            @click="$emit('edit-layer')"
          >
            <el-icon><Edit /></el-icon>
            编辑
          </el-link>
          <el-link type="danger" class="action-link" @click="handleDelete">
            <el-icon><Delete /></el-icon>
            删除
          </el-link>
        </div>
      </div>

      <div class="card-title">{{ title }}</div>
      <div class="card-description">{{ description }}</div>

      <div class="card-details">
        <el-link type="primary" class="action-link">
          <el-icon><View /></el-icon>
          查看详情
        </el-link>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import {
  Setting,
  Edit,
  Delete,
  DataLine,
  View,
  List
} from "@element-plus/icons-vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import { deleteDomain, addMenuLayer } from "@/api/data-warehouse";
import { getMenuNav } from "@/api/user";

const router = useRouter();

const emit = defineEmits<{
  (e: "edit-layer"): void;
  (e: "refresh"): void;
}>();

const props = defineProps<{
  title: string;
  description: string;
  domainId: number;
  owner: string;
  layerName: string;
}>();

const handleCreateTable = () => {
  if (!props.domainId) {
    ElMessage.error("数据域ID不存在，请检查数据完整性");
    return;
  }
  router.push(
    `/data-warehouse/table-detail?mode=view&domainId=${props.domainId}`
  );
};

const handleDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除数据域"${props.title}"吗？删除后将无法恢复。`,
      "警告",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    );

    // 删除数据域
    await deleteDomain([props.domainId]);

    // 更新菜单层级
    await addMenuLayer({
      layeringName: props.layerName,
      domainName: props.title,
      menuType: 2, // 数据域类型为2
      operation: 2 // 删除操作为2
    });

    // 刷新菜单导航
    await getMenuNav();

    ElMessage.success("删除成功");
    emit("refresh"); // 通知父组件刷新列表
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除失败:", error);
      ElMessage.error("删除失败");
    }
  }
};
</script>

<style lang="scss" scoped>
.layer-card {
  width: 360px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  background-color: #fff;

  :deep(.el-card__body) {
    padding: 0;
  }
}

.card-content {
  position: relative;
  padding: 16px;

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;

    .card-icon {
      width: 56px;
      height: 56px;
      background-color: #e8f5e9;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      :deep(svg) {
        font-size: 24px;
        color: #4caf50;
      }
    }

    .card-actions {
      display: flex;
      justify-content: flex-end;
      gap: 2px;
    }
  }
}

.card-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 12px;
  color: #333;
}

.card-description {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 8px;
  height: 112px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card-details {
  // position: absolute;
  // bottom: 20px;
  // right: 16px;
  display: flex;
  justify-content: flex-end;
}

.action-link {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  margin-left: 12px;
  font-size: 14px;
  transition: color 0.2s;

  :deep(.el-icon) {
    font-size: 16px;
  }
}
</style>
