<template>
  <el-dialog
    v-model="dialogVisible"
    :title="props.title"
    width="500px"
    :close-on-click-modal="false"
    @closed="handleClosed"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      class="domain-form"
    >
      <el-form-item label="分层类别" prop="category">
        <el-select
          v-model="form.category"
          placeholder="请选择分层类别"
          style="width: 100%"
          disabled
        >
          <el-option
            v-for="item in categoryOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="中文名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入中文名称" />
      </el-form-item>

      <el-form-item label="简称" prop="shortName">
        <el-input v-model="form.shortName" placeholder="请输入简称" />
      </el-form-item>

      <el-form-item label="负责人" prop="ownPerson">
        <el-select
          v-model="form.ownPerson"
          placeholder="请选择负责人"
          style="width: 100%"
          @change="handleOwnerChange"
        >
          <el-option
            v-for="item in ownerOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { ElMessage } from "element-plus";
import { getUserList } from "@/api/data-warehouse";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: "新增数据域"
  },
  parentLayer: {
    type: Object,
    default: () => null
  },
  currentDomain: {
    type: Object,
    default: () => null
  }
});

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "submit", data: Record<string, any>): void;
}>();

// 表单引用
const formRef = ref<FormInstance>();
const loading = ref(false);

// 负责人选项
const ownerOptions = ref<{ label: string; value: number }[]>([]);

// 获取负责人列表
const fetchOwnerList = async () => {
  try {
    const { data, code, msg } = await getUserList({ page: 1, limit: 100 });
    if (code === 0 && data) {
      ownerOptions.value = data.list.map(user => ({
        label: user.realName,
        value: user.id
      }));
    } else {
      ElMessage.error(msg || "获取负责人列表失败");
    }
  } catch (error) {
    console.error("获取负责人列表失败:", error);
    ElMessage.error("获取负责人列表失败");
  }
};

// 分层类别选项
const categoryOptions = computed(() => {
  if (!props.parentLayer) return [];
  return [
    {
      value: props.parentLayer.id,
      label: props.parentLayer.name
    }
  ];
});

// 表单数据
const form = ref({
  id: null as number | null,
  category: "",
  name: "",
  shortName: "",
  ownPerson: null as number | null,
  ownPersonName: ""
});

// 表单验证规则
const rules = computed<FormRules>(() => ({
  category: [
    {
      required: true,
      message: "请选择分层类别",
      trigger: "change"
    }
  ],
  name: [{ required: true, message: "请输入中文名称", trigger: "blur" }],
  shortName: [{ required: true, message: "请输入简称", trigger: "blur" }],
  ownPerson: [{ required: true, message: "请选择负责人", trigger: "change" }]
}));

// 对话框可见性
const dialogVisible = ref(props.visible);

// 监听visible属性变化
watch(
  () => props.visible,
  val => {
    dialogVisible.value = val;
    if (val) {
      // 打开弹窗时获取负责人列表
      fetchOwnerList();

      // 重置表单数据
      resetForm();

      // 设置分层类别
      if (props.parentLayer) {
        form.value.category = props.parentLayer.id;
      }

      // 如果是编辑模式，填充数据
      if (props.currentDomain) {
        form.value = {
          id: props.currentDomain.id,
          category: props.parentLayer?.id || "",
          name: props.currentDomain.name,
          shortName: props.currentDomain.shortName,
          ownPerson: props.currentDomain.ownPerson,
          ownPersonName: props.currentDomain.ownPersonName
        };
      }

      // 清除之前的验证状态
      nextTick(() => {
        formRef.value?.clearValidate();
      });
    }
  }
);

// 监听对话框可见性变化
watch(dialogVisible, val => {
  emit("update:visible", val);
});

// 处理负责人选择变化
const handleOwnerChange = (value: number) => {
  const selectedOwner = ownerOptions.value.find(
    option => option.value === value
  );
  if (selectedOwner) {
    form.value.ownPersonName = selectedOwner.label;
  }
};

// 重置表单
const resetForm = () => {
  form.value = {
    id: null,
    category: props.parentLayer?.id || "",
    name: "",
    shortName: "",
    ownPerson: null,
    ownPersonName: ""
  };
};

// 取消
const handleCancel = () => {
  dialogVisible.value = false;
  formRef.value?.clearValidate();
  resetForm();
};

// 关闭后重置表单
const handleClosed = () => {
  formRef.value?.clearValidate();
  resetForm();
};

// 提交
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    loading.value = true;
    await formRef.value.validate();
    emit("submit", { ...form.value });
    dialogVisible.value = false;
  } catch (error) {
    console.error("表单验证失败:", error);
  } finally {
    loading.value = false;
  }
};
</script>

<script lang="ts">
export default {
  name: "AddDomainDialog"
};
</script>

<style scoped>
.domain-form {
  padding: 20px 20px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-dialog__body) {
  padding-top: 0;
  padding-bottom: 0;
}
</style>
