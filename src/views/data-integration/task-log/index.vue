<template>
  <div>
    <div class="content-container">
      <div class="page-header">
        <span class="page-title">任务日志</span>
        <div class="filter-container">
          <el-input
            v-model="searchTaskName"
            placeholder="任务名称"
            style="width: 180px; margin-right: 10px"
            clearable
          />
          <el-select
            v-model="searchStatus"
            placeholder="执行状态"
            style="width: 180px; margin-right: 10px"
            clearable
          >
            <el-option label="成功" value="成功" />
            <el-option label="失败" value="失败" />
            <el-option label="运行中" value="运行中" />
          </el-select>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="max-width: 240px; margin-right: 10px"
          />
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </div>
      </div>
      <el-table :data="tableData" style="width: 100%" class="custom-table">
        <el-table-column prop="id" label="日志ID" width="80" />
        <el-table-column prop="taskName" label="任务名称" min-width="150" />
        <el-table-column prop="status" label="执行状态" min-width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)" size="large">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="startTime" label="开始时间" min-width="180" />
        <el-table-column prop="endTime" label="结束时间" min-width="180" />
        <el-table-column prop="duration" label="耗时(秒)" width="100" />
        <el-table-column prop="dataCount" label="处理数据量" min-width="120" />
        <el-table-column label="操作" width="180">
          <template #default="scope">
            <el-button type="text" @click="viewDetail(scope.row)"
              >查看详情</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";

defineOptions({
  name: "TaskLog"
});

// 搜索条件
const searchTaskName = ref("");
const searchStatus = ref("");
const dateRange = ref([]);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(100);

// 模拟数据
const tableData = ref([
  {
    id: 1,
    taskName: "MySQL到HDFS同步",
    status: "成功",
    startTime: "2023-01-01 10:00:00",
    endTime: "2023-01-01 10:05:30",
    duration: 330,
    dataCount: "10,000"
  },
  {
    id: 2,
    taskName: "PostgreSQL到Hive同步",
    status: "失败",
    startTime: "2023-01-02 11:30:00",
    endTime: "2023-01-02 11:32:10",
    duration: 130,
    dataCount: "5,000"
  },
  {
    id: 3,
    taskName: "Oracle到MySQL同步",
    status: "运行中",
    startTime: "2023-01-03 14:15:00",
    endTime: "-",
    duration: "-",
    dataCount: "正在处理"
  }
]);

// 获取状态样式
const getStatusType = status => {
  switch (status) {
    case "成功":
      return "success";
    case "失败":
      return "danger";
    case "运行中":
      return "warning";
    default:
      return "";
  }
};

// 处理函数
const handleSearch = () => {
  console.log("搜索", {
    taskName: searchTaskName.value,
    status: searchStatus.value,
    dateRange: dateRange.value
  });
};

const resetSearch = () => {
  searchTaskName.value = "";
  searchStatus.value = "";
  dateRange.value = [];
  handleSearch();
};

const viewDetail = row => {
  console.log("查看详情", row);
};

const handleSizeChange = val => {
  pageSize.value = val;
  // 重新加载数据
};

const handleCurrentChange = val => {
  currentPage.value = val;
  // 重新加载数据
};
</script>

<style scoped>
.content-container {
  background-color: #fff;
  border-radius: 4px;
  padding: 24px;
}

.page-header {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 24px;
}

.page-title {
  font-size: 18px;
  font-weight: 500;
}

.filter-container {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.custom-table {
  font-size: 14px;
}

:deep(.el-table__row) {
  height: 55px;
}

:deep(.el-tag) {
  font-size: 14px;
  padding: 0 10px;
  height: 32px;
  line-height: 30px;
}

:deep(.el-table .el-button) {
  margin-right: 8px;
  padding: 8px 0;
  font-size: 14px;
}
</style>
