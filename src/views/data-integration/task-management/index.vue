<template>
  <div>
    <div class="content-container">
      <div class="page-header">
        <span class="page-title">任务管理</span>
        <div class="filter-container">
          <el-input
            v-model="searchTaskName"
            placeholder="任务名称"
            style="width: 180px; margin-right: 10px"
            clearable
          />
          <el-select
            v-model="searchTaskType"
            placeholder="任务类型"
            style="width: 180px; margin-right: 10px"
            clearable
          >
            <el-option label="周期性增量" value="周期性增量" />
            <el-option label="一次性全量" value="一次性全量" />
            <el-option label="周期性全量" value="周期性全量" />
          </el-select>
          <el-select
            v-model="searchStatus"
            placeholder="状态"
            style="width: 180px; margin-right: 10px"
            clearable
          >
            <el-option label="已完成" value="已完成" />
            <el-option label="已停止" value="已停止" />
            <el-option label="运行中" value="运行中" />
            <el-option label="停止中" value="停止中" />
            <el-option label="未执行" value="未执行" />
          </el-select>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </div>
      </div>
      <div class="button-container">
        <el-button type="primary" @click="createTask">新增任务</el-button>
      </div>
      <el-table
        :data="tableData"
        style="width: 100%"
        class="custom-table"
        :cell-style="{ padding: '12px 8px' }"
        :header-cell-style="{ padding: '12px 8px', backgroundColor: '#F5F7FA' }"
      >
        <el-table-column prop="id" label="任务ID" width="120" />
        <el-table-column prop="name" label="任务名称" min-width="150" />
        <el-table-column
          prop="sourceType"
          label="来源数据源类型"
          min-width="120"
        />
        <el-table-column
          prop="sourceDataName"
          label="来源数据源名称"
          min-width="120"
        />
        <el-table-column
          prop="targetType"
          label="目标数据源类型"
          min-width="120"
        />
        <el-table-column
          prop="targetDataName"
          label="目标数据源名称"
          min-width="120"
        />
        <el-table-column prop="taskType" label="任务类型" min-width="200" />
        <el-table-column prop="status" label="状态" min-width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)" size="large">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="createTime"
          label="上次执行时间"
          min-width="180"
        />
        <el-table-column label="操作" fixed="right" width="280">
          <template #default="scope">
            <el-button
              v-if="scope.row.status !== '运行中'"
              type="text"
              @click="handleRun(scope.row)"
              >执行</el-button
            >
            <el-button type="text" @click="handleEdit(scope.row)"
              >编辑</el-button
            >
            <el-button type="text" @click="handleDelete(scope.row)"
              >删除</el-button
            >
            <el-button
              v-if="scope.row.status === '运行中'"
              type="text"
              @click="handleStop(scope.row)"
              >停止</el-button
            >
            <el-button type="text" @click="viewDetail(scope.row)"
              >日志</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";

defineOptions({
  name: "TaskManagement"
});

// 搜索条件
const searchTaskName = ref("");
const searchTaskType = ref("");
const searchStatus = ref("");
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(100);

// 模拟数据
const tableData = ref([
  {
    id: "2023031906",
    name: "同步168MySQL数据",
    sourceType: "Mysql",
    sourceDataName: "测试数据源1",
    targetType: "MaxCompute",
    targetDataName: "测试数据源11",
    taskType: "周期性增量-整库表结同步",
    status: "已完成",
    createTime: "2023-03-19 09:01:39 – 2023-03-19 11:28:11"
  },
  {
    id: "2023031905",
    name: "同步167Oracle数据",
    sourceType: "Oracle",
    sourceDataName: "测试数据源2",
    targetType: "OSS",
    targetDataName: "测试数据源12",
    taskType: "一次性全量-整库表结同步",
    status: "已停止",
    createTime: "2023-03-19 09:01:39 – 2023-03-19 11:28:11"
  },
  {
    id: "2023031904",
    name: "同步166DB2数据",
    sourceType: "DB2",
    sourceDataName: "测试数据源3",
    targetType: "MaxCompute",
    targetDataName: "测试数据源13",
    taskType: "周期性全量-整库表结同步",
    status: "停止中",
    createTime: "2023-03-19 09:01:39 – "
  },
  {
    id: "2023031903",
    name: "同步165MySQL数据",
    sourceType: "Mysql",
    sourceDataName: "测试数据源4",
    targetType: "MaxCompute",
    targetDataName: "测试数据源14",
    taskType: "周期性增量-整库表结同步",
    status: "运行中",
    createTime: "2023-03-19 09:01:39 – "
  },
  {
    id: "2023031902",
    name: "同步164Oracle数据",
    sourceType: "Oracle",
    sourceDataName: "测试数据源5",
    targetType: "OSS",
    targetDataName: "测试数据源15",
    taskType: "一次性全量-整库表结同步",
    status: "已完成",
    createTime: "2023-03-19 09:01:39 – 2023-03-19 11:28:11"
  },
  {
    id: "2023031901",
    name: "同步163DB2数据",
    sourceType: "DB2",
    sourceDataName: "测试数据源6",
    targetType: "MaxCompute",
    targetDataName: "测试数据源16",
    taskType: "周期性全量-整库表结同步",
    status: "未执行",
    createTime: "—"
  }
]);

// 获取状态样式
const getStatusType = status => {
  switch (status) {
    case "已完成":
      return "success";
    case "已停止":
      return "info";
    case "运行中":
      return "primary";
    case "停止中":
      return "warning";
    case "未执行":
      return "";
    default:
      return "";
  }
};

// 处理函数
const handleSearch = () => {
  console.log("搜索", {
    taskName: searchTaskName.value,
    taskType: searchTaskType.value,
    status: searchStatus.value
  });
  // 在实际应用中这里应该调用API获取过滤数据
};

const resetSearch = () => {
  searchTaskName.value = "";
  searchTaskType.value = "";
  searchStatus.value = "";
  handleSearch();
};

const createTask = () => {
  console.log("新增任务");
};

const handleEdit = row => {
  console.log("编辑", row);
};

const handleDelete = row => {
  console.log("删除", row);
};

const handleRun = row => {
  console.log("执行", row);
};

const handleStop = row => {
  console.log("停止", row);
};

const viewDetail = row => {
  console.log("查看日志", row);
};

const handleSizeChange = val => {
  pageSize.value = val;
  // 重新加载数据
};

const handleCurrentChange = val => {
  currentPage.value = val;
  // 重新加载数据
};
</script>

<style scoped>
.content-container {
  background-color: #fff;
  border-radius: 4px;
  padding: 24px;
}

.page-header {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;
}

.filter-container {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 5px;
  margin-top: 10px;
}

.button-container {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 20px;
}

.page-title {
  font-size: 18px;
  font-weight: 500;
}

.custom-table {
  font-size: 14px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

:deep(.el-table .el-button) {
  margin-right: 8px;
  padding: 8px 0;
  font-size: 14px;
}

:deep(.el-table__row) {
  height: 55px;
}

:deep(.el-tag) {
  font-size: 14px;
  padding: 0 10px;
  height: 32px;
  line-height: 30px;
}
</style>
