<template>
  <div>
    <div class="content-container">
      <div class="page-header">
        <span class="page-title">数据源管理</span>
        <div class="filter-container">
          <el-input
            v-model="searchName"
            placeholder="数据源名称"
            style="width: 180px; margin-right: 10px"
            clearable
          />
          <el-select
            v-model="searchType"
            placeholder="数据源类型"
            style="width: 180px; margin-right: 10px"
            clearable
          >
            <el-option
              v-for="item in DATABASE_TYPES"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </div>
      </div>
      <div class="button-container">
        <el-button type="primary" @click="dialogVisible = true"
          >添加数据源</el-button
        >
        <el-button
          type="danger"
          :disabled="!selectedRows.length"
          :loading="batchDeleteLoading"
          @click="handleBatchDelete"
          >批量删除</el-button
        >
        <el-button
          type="primary"
          :disabled="!selectedRows.length"
          @click="handleExport"
          >导出数据源</el-button
        >
      </div>
      <el-table
        :data="tableData"
        style="width: 100%"
        class="custom-table"
        :cell-style="{ padding: '12px 8px' }"
        :header-cell-style="{ padding: '12px 8px', backgroundColor: '#F5F7FA' }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="数据源名称" min-width="150">
          <template #default="scope">
            <el-button type="primary" link @click="handleShowDetail(scope.row)">
              {{ scope.row.name }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="databaseType" label="数据源类型" min-width="120">
          <template #default="scope">
            {{ DATABASE_TYPE_MAP[scope.row.databaseType] }}
          </template>
        </el-table-column>
        <el-table-column prop="databaseIp" label="主机地址" min-width="150" />
        <el-table-column prop="databasePort" label="端口" width="100" />
        <el-table-column prop="createTime" label="创建时间" min-width="180" />
        <el-table-column label="操作" width="300">
          <template #default="scope">
            <el-button
              type="primary"
              link
              size="small"
              @click="handleEdit(scope.row)"
              >编辑</el-button
            >
            <el-button
              type="primary"
              link
              size="small"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
            <el-button
              type="primary"
              link
              size="small"
              :loading="testLoading[scope.row.id]"
              @click="handleTest(scope.row)"
              >测试连接</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 使用抽取的数据源对话框组件 -->
    <DataSourceDialog
      v-model:visible="dialogVisible"
      :edit-data="currentRow"
      @save="handleSaveDataSource"
      @test="handleTestConnection"
      @reset="handleDialogReset"
    />

    <!-- 数据源详情弹窗 -->
    <DataSourceDetailDialog
      v-model:visible="detailVisible"
      :detail-data="detailData"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import DataSourceDialog from "./components/DataSourceDialog.vue";
import DataSourceDetailDialog from "./components/DataSourceDetailDialog.vue";
import { DATABASE_TYPES, DATABASE_TYPE_MAP } from "@/constants/database";
import {
  addDatabase,
  updateDatabase,
  getDatabasePageList,
  deleteDatabase,
  testDatabaseConnection,
  batchDeleteDatabase,
  exportDatabase
} from "@/api/database";

defineOptions({
  name: "DataSource"
});

// 搜索条件
const searchName = ref("");
const searchType = ref("");
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 对话框控制
const dialogVisible = ref(false);
const currentRow = ref(null);

// 数据
const tableData = ref([]);

// 添加一个对象来存储每一行的loading状态
const testLoading = ref({});

// 批量删除加载状态
const batchDeleteLoading = ref(false);

// 详情弹窗控制
const detailVisible = ref(false);
const detailData = ref(null);

// 处理函数
const handleSearch = async () => {
  try {
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      name: searchName.value,
      databaseType: searchType.value
    };
    const res = await getDatabasePageList(params);
    if (res.code === 0) {
      tableData.value = res.data?.list || [];
      total.value = res.data?.total || 0;
    } else {
      ElMessage.error(res.msg || "获取数据失败");
    }
  } catch (error) {
    ElMessage.error("获取数据失败，请稍后重试");
  }
};

const resetSearch = () => {
  searchName.value = "";
  searchType.value = "";
  currentPage.value = 1;
  handleSearch();
};

const handleEdit = row => {
  console.log("编辑", row);
  currentRow.value = { ...row }; // 保存当前编辑的行数据
  dialogVisible.value = true; // 打开弹窗
};

const handleDelete = row => {
  ElMessageBox.confirm(`确定要删除数据源 "${row.name}" 吗？`, "删除确认", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(async () => {
      try {
        const res = await deleteDatabase(row.id);
        if (res.code === 0) {
          ElMessage.success("删除成功");
          handleSearch();
        } else {
          ElMessage.error(res.msg || "删除失败");
        }
      } catch (error) {
        console.error("删除数据源失败:", error);
        ElMessage.error("删除失败，请稍后重试");
      }
    })
    .catch(() => {
      // 用户取消删除
    });
};

const handleTest = async row => {
  try {
    // 设置当前行的loading状态
    testLoading.value[row.id] = true;

    // 调用测试连接API
    const res = await testDatabaseConnection(row);

    if (res.code === 0) {
      ElMessage.success("连接测试成功");
    } else {
      ElMessage.error(res.msg || "连接测试失败");
    }
  } catch (error) {
    console.error("测试连接失败:", error);
    ElMessage.error("测试连接失败，请检查数据源配置");
  } finally {
    // 无论成功失败都关闭loading
    testLoading.value[row.id] = false;
  }
};

// 处理来自对话框组件的事件
const handleTestConnection = formData => {
  console.log("测试连接", formData);
  // 这里应调用后端API进行连接测试
};

const handleSaveDataSource = async formData => {
  try {
    let res;
    if (formData.id) {
      // 修改
      res = await updateDatabase(formData.id, formData);
    } else {
      // 新增
      res = await addDatabase(formData);
    }
    if (res.code === 0) {
      ElMessage.success(formData.id ? "数据源修改成功" : "数据源添加成功");
      dialogVisible.value = false;
      handleSearch();
    } else {
      ElMessage.error(res.msg || "保存失败");
    }
  } catch (error) {
    console.error("保存数据源失败:", error);
    ElMessage.error("保存失败，请稍后重试");
  }
};

const handleSizeChange = val => {
  pageSize.value = val;
  currentPage.value = 1;
  handleSearch();
};

const handleCurrentChange = val => {
  currentPage.value = val;
  handleSearch();
};

const selectedRows = ref([]);

// 处理表格多选
const handleSelectionChange = selection => {
  selectedRows.value = selection;
};

// 处理导出
const handleExport = async () => {
  if (!selectedRows.value.length) {
    ElMessage.warning("请选择需要导出的数据源");
    return;
  }

  try {
    // 获取选中行的ID数组
    const ids = selectedRows.value.map(row => row.id);
    const res = await exportDatabase(ids);

    // 创建Blob对象并下载
    const blob = new Blob([res], { type: "application/vnd.ms-excel" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.href = url;
    link.download = `数据源列表_${new Date().getTime()}.xlsx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    ElMessage.success("导出成功");
  } catch (error) {
    console.error("导出失败:", error);
    ElMessage.error("导出失败，请稍后重试");
  }
};

// 添加对话框重置处理函数
const handleDialogReset = () => {
  // 重置当前编辑行为null
  currentRow.value = null;
};

// 批量删除
const handleBatchDelete = () => {
  if (!selectedRows.value.length) {
    ElMessage.warning("请选择需要删除的数据源");
    return;
  }

  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedRows.value.length} 个数据源吗？`,
    "批量删除确认",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  )
    .then(async () => {
      try {
        batchDeleteLoading.value = true;
        // 获取选中行的ID数组
        const ids = selectedRows.value.map(row => row.id);
        const res = await batchDeleteDatabase(ids);

        if (res.code === 0) {
          ElMessage.success("批量删除成功");
          // 刷新数据列表
          handleSearch();
        } else {
          ElMessage.error(res.msg || "批量删除失败");
        }
      } catch (error) {
        console.error("批量删除失败:", error);
        ElMessage.error("批量删除失败，请稍后重试");
      } finally {
        batchDeleteLoading.value = false;
      }
    })
    .catch(() => {
      // 用户取消删除
    });
};

const handleShowDetail = row => {
  detailData.value = row;
  detailVisible.value = true;
};

// 页面加载时自动获取数据
onMounted(() => {
  handleSearch();
});
</script>

<style scoped>
.content-container {
  background-color: #fff;
  border-radius: 4px;
  padding: 24px;
}

.page-header {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;
}

.filter-container {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 5px;
  margin-top: 10px;
}

.button-container {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 20px;
}

.page-title {
  font-size: 18px;
  font-weight: 500;
}

.custom-table {
  font-size: 14px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

:deep(.el-table .el-button) {
  margin-right: 8px;
  padding: 8px 0;
  font-size: 14px;
}

:deep(.el-table__row) {
  height: 55px;
}
</style>
