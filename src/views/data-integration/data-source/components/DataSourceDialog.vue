<template>
  <el-dialog
    v-model="dialogVisible"
    :title="props.editData ? '编辑数据源' : '新增数据源'"
    width="40%"
    :close-on-click-modal="false"
  >
    <el-form
      ref="dataSourceFormRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      class="datasource-form"
    >
      <el-form-item label="数据源类型" prop="databaseType" required>
        <el-select
          v-model="form.databaseType"
          placeholder="请选择数据源类型"
          style="width: 100%"
        >
          <el-option
            v-for="item in DATABASE_TYPES"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="数据源名称" prop="name" required>
        <el-input v-model="form.name" placeholder="数据源名称" />
      </el-form-item>

      <el-form-item label="数据源描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="4"
          placeholder="数据源描述"
        />
      </el-form-item>

      <el-form-item label="jdbc连接" prop="jdbcUrl">
        <el-input v-model="form.jdbcUrl" placeholder="jdbc连接" />
      </el-form-item>

      <el-form-item label="数据库名" prop="databaseName" required>
        <el-input v-model="form.databaseName" placeholder="数据库名" />
      </el-form-item>

      <el-form-item label="Schema" prop="databaseSchema" required>
        <el-input v-model="form.databaseSchema" placeholder="Schema" />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="主机ip" prop="databaseIp" required>
            <el-input v-model="form.databaseIp" placeholder="主机ip" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="端口" prop="databasePort" required>
            <el-input v-model="form.databasePort" placeholder="端口" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="用户名" prop="userName" required>
            <el-input v-model="form.userName" placeholder="用户名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="密码" prop="password" required>
            <el-input
              v-model="form.password"
              type="password"
              placeholder="密码"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="集群模式" prop="clusterMode" required>
        <el-radio-group v-model="form.clusterMode">
          <el-radio value="1">单点模式</el-radio>
          <el-radio value="2">集群模式</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <div class="dialog-footer">
      <el-button type="primary" :loading="loading" @click="testConnection"
        >测试连接</el-button
      >
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="saveDataSource"
        >保存</el-button
      >
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, defineProps, defineEmits, watch } from "vue";
import { ElMessage } from "element-plus";
import type { FormInstance } from "element-plus";
import { DATABASE_TYPES, type DatabaseType } from "@/constants/database";
import type { DatabaseInfo } from "@/api/types/database";
import {
  addDatabase,
  updateDatabase,
  testDatabaseConnection
} from "@/api/database";
import { any } from "vue-types";

interface Props {
  visible: boolean;
  editData: DatabaseInfo | null;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  "update:visible": [value: boolean];
  save: [data: DatabaseInfo];
  test: [data: DatabaseInfo];
  reset: [];
}>();

// 响应父组件的visible变化
const dialogVisible = ref(props.visible);
watch(
  () => props.visible,
  val => {
    dialogVisible.value = val;
    // 当弹窗打开时，且不是编辑模式，重置表单
    if (val && !props.editData) {
      resetForm();
    }
  }
);
watch(dialogVisible, val => {
  emit("update:visible", val);
  // 当弹窗关闭时，通知父组件重置编辑数据
  if (!val) {
    emit("reset");
  }
});

// 重置表单函数
const resetForm = () => {
  // 重置表单时不触发验证
  dataSourceFormRef.value?.resetFields();

  // 手动重置表单数据
  Object.keys(form).forEach(key => {
    if (key === "clusterMode") {
      form[key] = "1";
    } else if (key === "id") {
      form[key] = null;
    } else {
      form[key] = "";
    }
  });
};

// 监听编辑数据变化
watch(
  () => props.editData,
  newVal => {
    if (newVal) {
      // 编辑模式 - 填充数据
      resetForm(); // 先重置表单

      // 将编辑数据赋值给表单，确保所有字段类型正确
      Object.keys(form).forEach(key => {
        // 跳过不存在的字段
        if (newVal[key] === undefined) return;

        // 特殊处理部分字段类型
        if (key === "clusterMode" || key === "databaseType") {
          // 确保这些字段是字符串类型
          form[key] = String(newVal[key]);
        } else {
          form[key] = newVal[key];
        }
      });

      // 如果缺少必填字段，设置默认值
      if (form.databaseSchema === undefined || form.databaseSchema === null) {
        form.databaseSchema = "";
      }

      console.log("编辑数据:", newVal, "表单数据:", form);
    }
  },
  { immediate: true } // 组件创建时立即执行一次
);

// 表单引用
const dataSourceFormRef = ref<FormInstance | null>(null);

// 表单数据类型
interface FormState extends Omit<DatabaseInfo, "id"> {
  id: number | null;
}

// 表单数据
const form = reactive<FormState>({
  id: null,
  name: "",
  databaseType: "",
  databaseIp: "",
  databasePort: "",
  databaseName: "",
  databaseSchema: "",
  userName: "",
  password: "",
  jdbcUrl: "",
  description: "",
  clusterMode: "1"
});

// 表单验证规则
const rules = {
  databaseType: [
    { required: true, message: "请选择数据源类型", trigger: "change" }
  ],
  name: [{ required: true, message: "请输入数据源名称", trigger: "blur" }],
  jdbcUrl: [{ message: "请输入JDBC连接", trigger: "blur" }],
  databaseName: [
    { required: true, message: "请输入数据库名", trigger: "blur" }
  ],
  databaseSchema: [
    { required: true, message: "请输入Schema", trigger: "blur" }
  ],
  databaseIp: [{ required: true, message: "请输入主机ip", trigger: "blur" }],
  databasePort: [{ required: true, message: "请输入端口", trigger: "blur" }],
  userName: [{ required: true, message: "请输入用户名", trigger: "blur" }],
  password: [{ required: true, message: "请输入密码", trigger: "blur" }],
  clusterMode: [
    { required: true, message: "请选择集群模式", trigger: "change" }
  ]
};

// 状态控制
const loading = ref(false);
const saveLoading = ref(false);

// 测试连接
const testConnection = () => {
  dataSourceFormRef.value?.validate(async valid => {
    if (valid) {
      try {
        loading.value = true;
        const res = await testDatabaseConnection(form);
        if (res.code === 0) {
          ElMessage.success("连接测试成功");
        } else {
          ElMessage.error(res.msg || "连接测试失败");
        }
      } catch (error) {
        ElMessage.error("连接测试失败，请检查配置");
      } finally {
        loading.value = false;
      }
    } else {
      ElMessage.warning("请填写完整的数据源信息");
    }
  });
};

// 保存数据源
const saveDataSource = async () => {
  // 只有点击保存按钮时才触发验证
  dataSourceFormRef.value?.validate(async valid => {
    if (valid) {
      try {
        saveLoading.value = true;
        // 创建数据副本，避免直接修改原始表单数据
        const formData = { ...form };

        // 新增数据源时，删除id字段
        if (!formData.id) {
          delete formData.id;
        }

        // 通过事件通知父组件
        emit("save", formData);
        // 关闭对话框由父组件控制
      } catch (error) {
        console.error("保存失败:", error);
      } finally {
        saveLoading.value = false;
      }
    }
  });
};

// 取消
const handleCancel = () => {
  // 关闭弹窗前清除表单验证信息
  dataSourceFormRef.value?.clearValidate();
  dialogVisible.value = false;
};
</script>

<style scoped>
.datasource-form {
  margin: 20px 0;
  padding: 0 10px;
}

.dialog-footer {
  margin-top: 30px;
  padding: 10px 0;
  display: flex;
  justify-content: center;
  gap: 20px;
  border-top: 1px solid #ebeef5;
}

:deep(.el-dialog__body) {
  padding: 10px 15px;
}

:deep(.el-dialog__header) {
  padding: 10px 15px;
  border-bottom: 1px solid #ebeef5;
}
</style>
