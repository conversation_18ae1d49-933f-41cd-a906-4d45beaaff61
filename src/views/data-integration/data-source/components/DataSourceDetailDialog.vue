<template>
  <el-dialog
    v-model="dialogVisible"
    title="数据源详情"
    width="600px"
    :close-on-click-modal="false"
    class="datasource-detail-dialog"
  >
    <div class="detail-content">
      <div class="detail-item">
        <span class="item-label">数据源名称：</span>
        <span class="item-value">{{ detailData?.name }}</span>
      </div>
      <div class="detail-item">
        <span class="item-label">数据源类型：</span>
        <span class="item-value">{{
          DATABASE_TYPE_MAP[detailData?.databaseType] ?? "-"
        }}</span>
      </div>
      <div class="detail-item">
        <span class="item-label">数据源描述：</span>
        <span class="item-value">{{ detailData?.description || "-" }}</span>
      </div>
      <div class="detail-item">
        <span class="item-label">JDBC连接：</span>
        <el-tooltip
          :content="detailData?.jdbcUrl"
          placement="top"
          :show-after="500"
        >
          <span class="item-value jdbc-url">{{
            detailData?.jdbcUrl || "-"
          }}</span>
        </el-tooltip>
      </div>
      <div class="detail-item">
        <span class="item-label">数据库名：</span>
        <span class="item-value">{{ detailData?.databaseName }}</span>
      </div>
      <div class="detail-item">
        <span class="item-label">Schema：</span>
        <span class="item-value">{{ detailData?.databaseSchema }}</span>
      </div>
      <div class="detail-item">
        <span class="item-label">主机IP：</span>
        <span class="item-value">{{ detailData?.databaseIp }}</span>
      </div>
      <div class="detail-item">
        <span class="item-label">端口：</span>
        <span class="item-value">{{ detailData?.databasePort }}</span>
      </div>
      <div class="detail-item">
        <span class="item-label">用户名：</span>
        <span class="item-value">{{ detailData?.userName }}</span>
      </div>
      <div class="detail-item">
        <span class="item-label">集群模式：</span>
        <span class="item-value">{{
          detailData?.clusterMode === "1" ? "单点模式" : "集群模式"
        }}</span>
      </div>

      <div class="divider"></div>

      <div class="detail-item">
        <span class="item-label">创建时间：</span>
        <span class="item-value">{{ detailData?.createTime || "-" }}</span>
      </div>
      <div class="detail-item">
        <span class="item-label">创建人：</span>
        <span class="item-value">{{ detailData?.creatorName || "-" }}</span>
      </div>
      <div class="detail-item">
        <span class="item-label">修改时间：</span>
        <span class="item-value">{{ detailData?.updateTime || "-" }}</span>
      </div>
      <div class="detail-item">
        <span class="item-label">修改人：</span>
        <span class="item-value">{{ detailData?.updaterName || "-" }}</span>
      </div>
    </div>

    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import type { DatabaseInfo } from "@/api/types/database";
import { DATABASE_TYPE_MAP } from "@/constants/database";

interface Props {
  visible: boolean;
  detailData: DatabaseInfo | null;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  "update:visible": [value: boolean];
}>();

const dialogVisible = ref(props.visible);

watch(
  () => props.visible,
  val => {
    dialogVisible.value = val;
  }
);

watch(dialogVisible, val => {
  emit("update:visible", val);
});

const handleClose = () => {
  dialogVisible.value = false;
};
</script>

<style scoped>
.datasource-detail-dialog {
  :deep(.el-dialog__body) {
    padding: 24px;
  }
}

.detail-content {
  padding: 0 20px;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  line-height: 24px;
  font-size: 14px;
}

.item-label {
  width: 100px;
  color: #606266;
  text-align: right;
  margin-right: 12px;
  flex-shrink: 0;
}

.item-value {
  color: #303133;
  flex: 1;
}

.jdbc-url {
  display: inline-block;
  max-width: 400px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
}

.divider {
  height: 1px;
  background-color: #ebeef5;
  margin: 16px 0;
}

:deep(.el-dialog__header) {
  margin-right: 0;
  padding: 16px 20px;
  border-bottom: 1px solid #dcdfe6;
}

:deep(.el-dialog__footer) {
  padding: 16px 20px;
  border-top: 1px solid #dcdfe6;
}
</style>
