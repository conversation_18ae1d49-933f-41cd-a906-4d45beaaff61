<template>
  <div>
    <div class="content-container">
      <div class="page-header">
        <span class="page-title">任务运维</span>
        <div class="filter-container">
          <el-input
            v-model="searchTaskName"
            placeholder="任务名称"
            style="width: 150px; margin-right: 8px"
            clearable
          />
          <el-select
            v-model="searchStatus"
            placeholder="运行状态"
            style="width: 150px; margin-right: 8px"
            clearable
          >
            <el-option label="运行中" value="运行中" />
            <el-option label="已停止" value="已停止" />
            <el-option label="异常" value="异常" />
          </el-select>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </div>
      </div>
      <el-table :data="tableData" style="width: 100%" class="custom-table">
        <el-table-column prop="id" label="任务ID" width="80" />
        <el-table-column prop="name" label="任务名称" min-width="150" />
        <el-table-column prop="status" label="状态" min-width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)" size="large">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="lastRunTime"
          label="最近运行时间"
          min-width="180"
        />
        <el-table-column
          prop="nextRunTime"
          label="下次运行时间"
          min-width="180"
        />
        <el-table-column prop="cpu" label="CPU使用率" min-width="150">
          <template #default="scope">
            <el-progress
              :percentage="scope.row.cpu"
              :color="getCpuColor(scope.row.cpu)"
              :stroke-width="18"
            />
          </template>
        </el-table-column>
        <el-table-column prop="memory" label="内存使用率" min-width="150">
          <template #default="scope">
            <el-progress
              :percentage="scope.row.memory"
              :color="getMemoryColor(scope.row.memory)"
              :stroke-width="18"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="350">
          <template #default="scope">
            <el-button type="text" @click="handleStart(scope.row)"
              >启动</el-button
            >
            <el-button type="text" @click="handleStop(scope.row)"
              >停止</el-button
            >
            <el-button type="text" @click="handleRestart(scope.row)"
              >重启</el-button
            >
            <el-button type="text" @click="viewMonitor(scope.row)"
              >监控</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";

defineOptions({
  name: "TaskOps"
});

// 搜索条件
const searchTaskName = ref("");
const searchStatus = ref("");
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(100);

// 模拟数据
const tableData = ref([
  {
    id: 1,
    name: "MySQL到HDFS同步",
    status: "运行中",
    lastRunTime: "2023-01-01 10:00:00",
    nextRunTime: "2023-01-02 10:00:00",
    cpu: 45,
    memory: 60
  },
  {
    id: 2,
    name: "PostgreSQL到Hive同步",
    status: "已停止",
    lastRunTime: "2023-01-02 11:30:00",
    nextRunTime: "2023-01-03 11:30:00",
    cpu: 0,
    memory: 0
  },
  {
    id: 3,
    name: "Oracle到MySQL同步",
    status: "异常",
    lastRunTime: "2023-01-03 14:15:00",
    nextRunTime: "-",
    cpu: 95,
    memory: 88
  }
]);

// 获取状态样式
const getStatusType = status => {
  switch (status) {
    case "运行中":
      return "success";
    case "已停止":
      return "info";
    case "异常":
      return "danger";
    default:
      return "";
  }
};

// 获取CPU颜色
const getCpuColor = percentage => {
  if (percentage < 70) return "#67c23a";
  if (percentage < 85) return "#e6a23c";
  return "#f56c6c";
};

// 获取内存颜色
const getMemoryColor = percentage => {
  if (percentage < 70) return "#67c23a";
  if (percentage < 85) return "#e6a23c";
  return "#f56c6c";
};

// 处理函数
const handleSearch = () => {
  console.log("搜索", {
    taskName: searchTaskName.value,
    status: searchStatus.value
  });
};

const resetSearch = () => {
  searchTaskName.value = "";
  searchStatus.value = "";
  handleSearch();
};

const handleStart = row => {
  console.log("启动", row);
};

const handleStop = row => {
  console.log("停止", row);
};

const handleRestart = row => {
  console.log("重启", row);
};

const handleKill = row => {
  console.log("终止", row);
};

const viewMonitor = row => {
  console.log("查看监控", row);
};

const handleSizeChange = val => {
  pageSize.value = val;
  // 重新加载数据
};

const handleCurrentChange = val => {
  currentPage.value = val;
  // 重新加载数据
};
</script>

<style scoped>
.content-container {
  background-color: #fff;
  border-radius: 4px;
  padding: 24px;
}

.page-header {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 24px;
}

.page-title {
  font-size: 18px;
  font-weight: 500;
}

.filter-container {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.custom-table {
  font-size: 14px;
}

:deep(.el-table .el-button) {
  margin-right: 8px;
  padding: 8px 0;
  font-size: 14px;
}

:deep(.el-table__row) {
  height: 55px;
}

:deep(.el-tag) {
  font-size: 14px;
  padding: 0 10px;
  height: 32px;
  line-height: 30px;
}
</style>
