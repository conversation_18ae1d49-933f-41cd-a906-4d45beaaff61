<template>
  <div class="table-detail-container">
    <div class="page-header">
      <h2>命名标准</h2>
    </div>
    <div class="page-content">
      <!-- 左侧树结构 -->
      <div class="table-list">
        <div class="header">
          <div class="action-buttons">
            <el-button type="primary" @click="handleAddCategory"
              >新建目录</el-button
            >
          </div>
          <div class="filter-section">
            <el-input
              v-model="categoryFilter.keyword"
              placeholder="请输入目录名称"
              clearable
              style="width: 100%"
            />
            <el-select
              v-model="categoryFilter.layeringType"
              placeholder="请选择数仓"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="item in warehouseOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
            <div class="filter-actions">
              <el-button type="primary" size="small" @click="handleApplyFilter"
                >查询</el-button
              >
              <el-button size="small" @click="handleResetTreeFilter"
                >重置</el-button
              >
            </div>
          </div>
        </div>
        <div v-loading="treeLoading" class="list">
          <el-tree
            :data="filterTreeData"
            :props="defaultProps"
            node-key="id"
            :default-expanded-keys="getAllNodeIds"
            @node-click="handleNodeClick"
          >
            <template #default="{ node, data }">
              <span
                :class="[
                  'custom-tree-node',
                  { active: currentCategory === data.id }
                ]"
              >
                {{ node.label }}
                <span class="node-actions">
                  <el-link
                    type="primary"
                    style="margin-right: 8px"
                    @click.stop="handleEditCategory(data)"
                  >
                    <el-icon><Edit /></el-icon>
                  </el-link>
                  <el-link
                    type="danger"
                    @click.stop="handleDeleteCategory(data)"
                  >
                    <el-icon><Delete /></el-icon>
                  </el-link>
                </span>
              </span>
            </template>
          </el-tree>
        </div>
      </div>
      <!-- 右侧内容 -->
      <div class="table-info">
        <!-- 单个容器包含所有内容 -->
        <el-card class="box-card">
          <div v-if="!currentCategory" class="empty-state">
            <el-empty description="请选择一个命名类别" />
          </div>
          <template v-else>
            <!-- 上部分：目录基本信息 -->
            <div class="info-section">
              <div class="section-title">目录信息</div>
              <div class="table-info-row">
                <div class="info-item">
                  <span class="label">目录名称：</span>
                  <span class="value">{{
                    getCategoryName(currentCategory)
                  }}</span>
                </div>
                <div class="info-item">
                  <span class="label">所属数仓：</span>
                  <span class="value">{{
                    getCategoryWarehouse(currentCategory)
                  }}</span>
                </div>
              </div>
              <div class="table-info-row">
                <div class="info-item" style="width: 100%">
                  <span class="label">目录描述：</span>
                  <span class="value">{{
                    getCategoryDescription(currentCategory) || "暂无描述"
                  }}</span>
                </div>
              </div>
            </div>

            <!-- 下部分：筛选、添加和列表展示 -->
            <div class="field-section">
              <div class="section-title">命名标准</div>
              <div class="filter-actions">
                <el-form :inline="true" class="filter-form" @submit.prevent>
                  <el-form-item label="中文名称：">
                    <el-input
                      v-model="filter.cnName"
                      placeholder="请输入"
                      clearable
                    />
                  </el-form-item>
                  <el-form-item label="英文名称：">
                    <el-input
                      v-model="filter.enName"
                      placeholder="请输入"
                      clearable
                    />
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="handleFilter"
                      >查询</el-button
                    >
                    <el-button @click="handleReset">重置</el-button>
                  </el-form-item>
                </el-form>
                <div class="toolbar-row">
                  <el-button type="primary" @click="handleAddRule"
                    >新增命名标准</el-button
                  >
                  <el-button
                    type="danger"
                    :disabled="!multipleSelection.length"
                    @click="handleBatchDelete"
                    >批量删除</el-button
                  >
                  <el-button @click="handleExport">导出命名标准</el-button>
                  <div v-if="multipleSelection.length" class="selected-tip">
                    已选 {{ multipleSelection.length }} 项
                  </div>
                </div>
              </div>
              <el-table
                :data="paginatedTableData"
                style="width: 100%"
                @selection-change="handleSelectionChange"
              >
                <el-table-column type="selection" width="50" />
                <el-table-column prop="id" label="序号" width="80" />
                <el-table-column
                  prop="name"
                  label="名称"
                  min-width="150"
                  show-overflow-tooltip
                />
                <el-table-column
                  prop="shortName"
                  label="缩写"
                  width="120"
                  show-overflow-tooltip
                />
                <el-table-column
                  prop="dir"
                  label="所属目录"
                  min-width="150"
                  show-overflow-tooltip
                >
                  <template #default="scope">
                    {{ getCategoryName(parseInt(scope.row.dir)) }}
                  </template>
                </el-table-column>
                <el-table-column prop="ruleType" label="命名规则" width="120">
                  <template #default="scope">
                    <el-tag :type="getRuleTypeTagType(scope.row.ruleType)">
                      {{ getRuleTypeName(scope.row.ruleType) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="长度限制" width="120">
                  <template #default="scope">
                    {{ scope.row.minLength }}-{{ scope.row.maxLength }}
                  </template>
                </el-table-column>
                <el-table-column
                  prop="matchPattern"
                  label="正则表达式"
                  min-width="180"
                  show-overflow-tooltip
                >
                  <template #default="scope">
                    <code v-if="scope.row.matchPattern" class="regex-code">
                      {{ scope.row.matchPattern }}
                    </code>
                    <span v-else class="text-gray-400">-</span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="comment"
                  label="描述"
                  min-width="200"
                  show-overflow-tooltip
                >
                  <template #default="scope">
                    <span v-if="scope.row.comment">{{
                      scope.row.comment
                    }}</span>
                    <span v-else class="text-gray-400">-</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="120" fixed="right">
                  <template #default="scope">
                    <el-link
                      type="primary"
                      style="margin-right: 10px"
                      @click="handleEdit(scope.row)"
                      >编辑</el-link
                    >
                    <el-link type="danger" @click="handleDelete(scope.row)"
                      >删除</el-link
                    >
                  </template>
                </el-table-column>
              </el-table>

              <!-- 分页控件 -->
              <div class="pagination-container">
                <el-pagination
                  v-model:current-page="currentPage"
                  v-model:page-size="pageSize"
                  :page-sizes="[10, 20, 50, 100]"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="totalCount"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                />
              </div>
            </div>
          </template>
        </el-card>
      </div>
    </div>

    <!-- 新建/编辑目录弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑目录' : '新增目录'"
      width="500px"
      :before-close="handleClose"
    >
      <el-form ref="categoryFormRef" :model="categoryForm" label-width="80px">
        <el-form-item label="父级目录" required>
          <el-select
            v-model="categoryForm.parentId"
            placeholder="请选择父级目录"
            style="width: 100%"
          >
            <el-option
              v-for="item in parentOptions"
              :key="item.id"
              :label="item.label"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="所属数仓" required>
          <el-select
            v-model="categoryForm.layeringType"
            placeholder="请选择所属数仓"
            style="width: 100%"
          >
            <el-option
              v-for="item in warehouseOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="目录名称" required>
          <el-input v-model="categoryForm.name" placeholder="目录名称" />
        </el-form-item>
        <el-form-item label="英文名称" required>
          <el-input
            v-model="categoryForm.englishName"
            placeholder="请输入英文名称"
          />
        </el-form-item>
        <el-form-item label="英文简称" required>
          <el-input
            v-model="categoryForm.englishShortName"
            placeholder="请输入英文简称"
          />
        </el-form-item>
        <el-form-item label="目录描述">
          <el-input
            v-model="categoryForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入目录描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitCategoryForm">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 新建/编辑命名标准弹窗 -->
    <NameDialog
      v-model:visible="nameDialogVisible"
      :is-edit="isNameEdit"
      :catalog-options="catalogOptions"
      :name-data="nameForm"
      @submit="submitNameForm"
      @cancel="handleNameDialogCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { Edit, Delete } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
import NameDialog from "./components/NameDialog.vue";
import {
  getDataStandardPage,
  saveDataStandard,
  getDataStandardDetail,
  updateDataStandard,
  deleteDataStandard,
  getTableNameStandardPage,
  saveTableNameStandard,
  updateTableNameStandard,
  deleteTableNameStandard,
  getLayeringTypeList,
  type TableNameStandardItem,
  type LayeringTypeItem
} from "@/api/data-standard";

// 定义目录接口类型
interface Category {
  id: number;
  pid?: number;
  children?: Category[];
  name: string;
  englishName?: string;
  englishShortName?: string;
  comment?: string;
  description?: string;
  parentId?: number;
  layeringType?: number;
  layeringTypeName?: string | null;
  creator?: number;
  createTime?: number;
  updater?: number;
  updateTime?: number;
  warehouseId?: number;
}

// 数仓数据
const warehouseList = ref<LayeringTypeItem[]>([]);

// 数仓选项
const warehouseOptions = computed(() => {
  return warehouseList.value.map(item => ({
    id: item.id,
    name: item.name
  }));
});

// 获取数仓分层列表
const fetchWarehouseList = async () => {
  try {
    const response = await getLayeringTypeList();
    if (response && response.code === 0) {
      warehouseList.value = response.data || [];
    } else {
      console.error("获取数仓分层列表失败:", response?.msg);
      ElMessage.error(response?.msg || "获取数仓分层列表失败");
    }
  } catch (error) {
    console.error("获取数仓分层列表失败:", error);
    ElMessage.error("获取数仓分层列表失败");
  }
};

// mock 树结构数据
const treeData = ref<Category[]>([
  {
    id: 1,
    pid: 0,
    name: "测试",
    englishName: "test",
    englishShortName: "test",
    comment: "测试目录",
    layeringType: 0,
    layeringTypeName: null,
    creator: 10000,
    createTime: 1750045225000,
    updater: 10000,
    updateTime: 1750045229000,
    children: [
      {
        id: 11,
        pid: 1,
        name: "子类A",
        englishName: "test",
        englishShortName: "test",
        comment: "测试子目录",
        layeringType: 0,
        layeringTypeName: null,
        creator: 10000,
        createTime: 1750045225000,
        updater: 10000,
        updateTime: 1750045229000
      },
      {
        id: 12,
        pid: 1,
        name: "子类B",
        englishName: "test",
        englishShortName: "test",
        comment: "测试子目录",
        layeringType: 0,
        layeringTypeName: null,
        creator: 10000,
        createTime: 1750045225000,
        updater: 10000,
        updateTime: 1750045229000
      }
    ]
  },
  {
    id: 2,
    pid: 0,
    name: "目录2",
    englishName: "test",
    englishShortName: "test",
    comment: "测试目录2",
    layeringType: 0,
    layeringTypeName: null,
    creator: 10000,
    createTime: 1750045225000,
    updater: 10000,
    updateTime: 1750045229000,
    children: [
      {
        id: 21,
        pid: 2,
        name: "子类C",
        englishName: "test",
        englishShortName: "test",
        comment: "测试子目录",
        layeringType: 0,
        layeringTypeName: null,
        creator: 10000,
        createTime: 1750045225000,
        updater: 10000,
        updateTime: 1750045229000
      }
    ]
  }
]);

const defaultProps = {
  children: "children",
  label: "name"
};

const currentCategory = ref<number | null>(null);

// 表名标准数据
const tableData = ref<TableNameStandardItem[]>([]);
const totalCount = ref(0);

const filter = ref({
  cnName: "",
  enName: ""
});

const multipleSelection = ref<any[]>([]);

const handleSelectionChange = (val: any[]) => {
  multipleSelection.value = val;
};

// 获取表名标准数据
const fetchTableNameData = async () => {
  if (!currentCategory.value) return;

  try {
    const response = await getTableNameStandardPage({
      dir: currentCategory.value,
      name: filter.value.cnName || undefined, // cnName映射为name参数
      page: currentPage.value,
      limit: pageSize.value
    });

    if (response && response.data) {
      tableData.value = response.data.list || [];
      totalCount.value = response.data.total || 0;
    }
  } catch (error) {
    console.error("获取表名标准数据失败:", error);
    ElMessage.error("获取表名标准数据失败");
  }
};

const handleNodeClick = async (data: any) => {
  currentCategory.value = data.id;
  console.log("选中节点:", data);
  // 获取目录详情
  try {
    const detail = await getDataStandardDetail(data.id);
    console.log("目录详情:", detail);
    // 你可以在这里将详情数据存到某个 ref 里用于展示
  } catch (e) {
    console.error("获取目录详情失败", e);
  }
  // 重置筛选条件
  filter.value.cnName = "";
  filter.value.enName = "";
  multipleSelection.value = [];
  // 重置分页
  currentPage.value = 1;
  // 获取表名标准数据
  fetchTableNameData();
};

// 弹窗相关
const dialogVisible = ref(false);
const isEdit = ref(false);
const editingCategoryId = ref<number | null>(null);
const categoryForm = ref({
  parentId: 0,
  layeringType: 0,
  name: "",
  englishName: "",
  englishShortName: "",
  description: ""
});
const categoryFormRef = ref();

// 命名标准弹窗相关
const nameDialogVisible = ref(false);
const isNameEdit = ref(false);
const editingNameId = ref<number | null>(null);
const nameForm = ref({
  name: "",
  dir: null as number | null,
  shortName: "",
  comment: "",
  minLength: 0,
  maxLength: 50,
  ruleType: 1,
  matchPattern: ""
});

// 获取可选的父级目录
const parentOptions = computed(() => {
  return [
    { id: 0, label: "根目录" },
    ...treeData.value.map(item => ({
      id: item.id,
      label: item.name
    }))
  ];
});

const handleClose = () => {
  dialogVisible.value = false;
  resetForm();
  isEdit.value = false;
  editingCategoryId.value = null;
};

const resetForm = () => {
  categoryForm.value = {
    parentId: 0,
    layeringType: 0,
    name: "",
    englishName: "",
    englishShortName: "",
    description: ""
  };
};

const handleAddCategory = () => {
  isEdit.value = false;
  editingCategoryId.value = null;
  resetForm();
  dialogVisible.value = true;
};

const submitCategoryForm = async () => {
  // 表单验证
  if (!categoryForm.value.parentId && categoryForm.value.parentId !== 0) {
    ElMessage.error("请选择父级目录");
    return;
  }
  if (!categoryForm.value.layeringType) {
    ElMessage.error("请选择所属数仓");
    return;
  }
  if (!categoryForm.value.name) {
    ElMessage.error("请输入目录名称");
    return;
  }
  if (!categoryForm.value.englishName) {
    ElMessage.error("请输入英文名称");
    return;
  }
  if (!categoryForm.value.englishShortName) {
    ElMessage.error("请输入英文简称");
    return;
  }

  if (isEdit.value && editingCategoryId.value !== null) {
    // 编辑目录，调用更新接口
    const params = {
      id: editingCategoryId.value,
      name: categoryForm.value.name,
      englishName: categoryForm.value.englishName,
      englishShortName: categoryForm.value.englishShortName,
      comment: categoryForm.value.description,
      layeringType: categoryForm.value.layeringType,
      pid: categoryForm.value.parentId
    };
    try {
      await updateDataStandard(params);
      ElMessage.success("目录修改成功");
      dialogVisible.value = false;
      fetchTreeData(); // 刷新树
      resetForm();
      isEdit.value = false;
      editingCategoryId.value = null;
    } catch (e) {
      ElMessage.error("目录修改失败");
    }
  } else {
    // 新增目录，调用接口
    const params = {
      name: categoryForm.value.name,
      englishName: categoryForm.value.englishName,
      englishShortName: categoryForm.value.englishShortName,
      comment: categoryForm.value.description,
      layeringType: categoryForm.value.layeringType,
      pid: categoryForm.value.parentId
    };
    try {
      await saveDataStandard(params);
      ElMessage.success("目录添加成功");
      dialogVisible.value = false;
      fetchTreeData(); // 刷新树
      resetForm();
      isEdit.value = false;
      editingCategoryId.value = null;
    } catch (e) {
      ElMessage.error("目录添加失败");
    }
  }
};

// 根据ID查找目录
const findCategoryById = (tree: Category[], id: number): Category | null => {
  for (const node of tree) {
    if (node.id === id) return node;
    if (node.children) {
      const found = findCategoryById(node.children, id);
      if (found) return found;
    }
  }
  return null;
};

// 获取目录名称
const getCategoryName = (categoryId: number) => {
  const category = findCategoryById(treeData.value, categoryId);
  return category ? category.name : "";
};

// 获取目录描述
const getCategoryDescription = (categoryId: number) => {
  const category = findCategoryById(treeData.value, categoryId);
  return category ? category.comment || category.description || "" : "";
};

// 获取命名规则名称
const getRuleTypeName = (ruleType: number) => {
  const ruleMap = {
    1: "无限制",
    2: "小驼峰",
    3: "大驼峰",
    4: "小写+下划线",
    5: "大写+下划线",
    6: "正则表达式"
  };
  return ruleMap[ruleType as keyof typeof ruleMap] || "未知";
};

// 获取命名规则标签类型
const getRuleTypeTagType = (
  ruleType: number
): "success" | "info" | "warning" | "danger" | "primary" | undefined => {
  const typeMap: Record<
    number,
    "success" | "info" | "warning" | "danger" | "primary" | undefined
  > = {
    1: undefined,
    2: "success",
    3: "warning",
    4: "info",
    5: "danger",
    6: "primary"
  };
  return typeMap[ruleType] || undefined;
};

// 获取可选的目录选项 - 树形结构数据
const catalogOptions = computed(() => {
  // 直接返回树形数据，TreeSelect组件会自动处理层级关系
  return treeData.value;
});

const handleAddRule = () => {
  if (!currentCategory.value) {
    ElMessage.warning("请先选择一个目录");
    return;
  }

  isNameEdit.value = false;
  editingNameId.value = null;

  // 重置表单并设置当前目录
  nameForm.value = {
    name: "",
    dir: currentCategory.value, // TreeSelect使用数值类型
    shortName: "",
    comment: "",
    minLength: 0,
    maxLength: 50,
    ruleType: 1,
    matchPattern: ""
  };

  nameDialogVisible.value = true;
};

const handleEdit = (row: any) => {
  isNameEdit.value = true;
  editingNameId.value = row.id;

  // 填充表单 - 使用新的字段结构
  nameForm.value = {
    name: row.name || "",
    dir: row.dir ? parseInt(row.dir) : null, // TreeSelect需要数值类型的ID
    shortName: row.shortName || "",
    comment: row.comment || "",
    minLength: row.minLength || 0,
    maxLength: row.maxLength || 50,
    ruleType: row.ruleType || 1,
    matchPattern: row.matchPattern || ""
  };

  nameDialogVisible.value = true;
};

const handleNameDialogCancel = () => {
  nameDialogVisible.value = false;
  nameForm.value = {
    name: "",
    dir: null,
    shortName: "",
    comment: "",
    minLength: 0,
    maxLength: 50,
    ruleType: 1,
    matchPattern: ""
  };
  isNameEdit.value = false;
  editingNameId.value = null;
};

const submitNameForm = async (formData: any) => {
  try {
    let response;

    if (isNameEdit.value && editingNameId.value) {
      // 编辑模式 - 调用修改API
      response = await updateTableNameStandard({
        id: editingNameId.value,
        name: formData.name,
        dir: formData.dir ? formData.dir.toString() : "", // 转换为字符串
        shortName: formData.shortName,
        comment: formData.comment,
        minLength: formData.minLength,
        maxLength: formData.maxLength,
        ruleType: formData.ruleType,
        matchPattern: formData.matchPattern
      });

      if (response && response.code === 0) {
        ElMessage.success("表名标准修改成功");
      } else {
        console.error("修改表名标准失败:", response?.msg);
        ElMessage.error(response?.msg || "修改表名标准失败");
        return;
      }
    } else {
      // 新增模式 - 调用新增API
      response = await saveTableNameStandard({
        name: formData.name,
        dir: formData.dir ? formData.dir.toString() : "", // 转换为字符串
        shortName: formData.shortName,
        comment: formData.comment,
        minLength: formData.minLength,
        maxLength: formData.maxLength,
        ruleType: formData.ruleType,
        matchPattern: formData.matchPattern
      });

      if (response && response.code === 0) {
        ElMessage.success("表名标准添加成功");
      } else {
        console.error("添加表名标准失败:", response?.msg);
        ElMessage.error(response?.msg || "添加表名标准失败");
        return;
      }
    }

    // 重新获取数据
    await fetchTableNameData();
  } catch (error) {
    console.error("操作表名标准失败:", error);
    ElMessage.error(isNameEdit.value ? "修改表名标准失败" : "添加表名标准失败");
    return;
  }

  // 关闭对话框并重置表单
  nameDialogVisible.value = false;
  nameForm.value = {
    name: "",
    dir: null,
    shortName: "",
    comment: "",
    minLength: 0,
    maxLength: 50,
    ruleType: 1,
    matchPattern: ""
  };
  isNameEdit.value = false;
  editingNameId.value = null;
};

const handleDelete = (row: any) => {
  ElMessageBox.confirm(`确定要删除表名标准 "${row.name}" 吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(async () => {
      try {
        const response = await deleteTableNameStandard(row.id);

        if (response && response.code === 0) {
          ElMessage.success("表名标准删除成功");
          // 重新获取数据
          await fetchTableNameData();
        } else {
          console.error("删除表名标准失败:", response?.msg);
          ElMessage.error(response?.msg || "删除表名标准失败");
        }
      } catch (error) {
        console.error("删除表名标准失败:", error);
        ElMessage.error("删除表名标准失败");
      }
    })
    .catch(() => {
      // 取消删除
    });
};

// 批量删除功能
const handleBatchDelete = async () => {
  if (!multipleSelection.value.length) {
    ElMessage.warning("请先选择要删除的表名标准");
    return;
  }

  const selectedNames = multipleSelection.value
    .map(item => item.name)
    .join("、");
  ElMessageBox.confirm(
    `确定要删除选中的 ${multipleSelection.value.length} 个表名标准吗？\n${selectedNames}`,
    "批量删除确认",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  )
    .then(async () => {
      try {
        // 由于当前API只支持单个删除，这里循环调用单个删除接口
        const deletePromises = multipleSelection.value.map(item =>
          deleteTableNameStandard(item.id)
        );

        const results = await Promise.allSettled(deletePromises);
        const successCount = results.filter(
          result => result.status === "fulfilled" && result.value?.code === 0
        ).length;

        if (successCount === multipleSelection.value.length) {
          ElMessage.success(`成功删除 ${successCount} 个表名标准`);
        } else if (successCount > 0) {
          ElMessage.warning(
            `成功删除 ${successCount} 个表名标准，${multipleSelection.value.length - successCount} 个删除失败`
          );
        } else {
          ElMessage.error("批量删除失败");
        }

        // 重新获取数据
        await fetchTableNameData();
        // 清空选择
        multipleSelection.value = [];
      } catch (error) {
        console.error("批量删除表名标准失败:", error);
        ElMessage.error("批量删除表名标准失败");
      }
    })
    .catch(() => {
      // 取消删除
    });
};

const handleExport = () => {
  // TODO: 导出命名标准逻辑
  alert("导出命名标准（mock）");
};

const handleFilter = () => {
  // 重置分页并重新获取数据
  currentPage.value = 1;
  fetchTableNameData();
};

const handleReset = () => {
  filter.value.cnName = "";
  filter.value.enName = "";
  // 重置分页并重新获取数据
  currentPage.value = 1;
  fetchTableNameData();
};

const handleEditCategory = (data: any) => {
  isEdit.value = true;
  editingCategoryId.value = data.id;

  // 查找父节点
  const parent = findParentNode(treeData.value, data.id);

  categoryForm.value = {
    parentId: parent ? parent.id : 0,
    layeringType: data.layeringType || 0,
    name: data.name,
    englishName: data.englishName || "",
    englishShortName: data.englishShortName || "",
    description: data.comment || ""
  };

  dialogVisible.value = true;
};

// 查找父节点
const findParentNode = (tree: Category[], id: number): Category | null => {
  for (const node of tree) {
    if (node.children) {
      for (const child of node.children) {
        if (child.id === id) return node;
      }
      const found = findParentNode(node.children, id);
      if (found) return found;
    }
  }
  return null;
};

const handleDeleteCategory = async (data: any) => {
  ElMessageBox.confirm(`确定要删除目录 "${data.name}" 吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(async () => {
      try {
        await deleteDataStandard([data.id]);
        ElMessage.success("目录删除成功");
        fetchTreeData();
        // 如果删除的是当前选中的节点，清空选择
        if (currentCategory.value === data.id) {
          currentCategory.value = null;
          tableData.value = [];
        }
      } catch (e) {
        ElMessage.error("目录删除失败");
      }
    })
    .catch(() => {
      // 取消删除
    });
};

// 获取数仓名称
const getWarehouseName = (warehouseId: number) => {
  const warehouse = warehouseList.value.find(item => item.id === warehouseId);
  return warehouse ? warehouse.name : "";
};

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);

const paginatedTableData = computed(() => {
  return tableData.value;
});

const handleSizeChange = (newSize: number) => {
  pageSize.value = newSize;
  currentPage.value = 1; // 重置到第一页
  fetchTableNameData();
};

const handleCurrentChange = (newPage: number) => {
  currentPage.value = newPage;
  fetchTableNameData();
};

// 新增获取所属数仓的函数
const getCategoryWarehouse = (categoryId: number) => {
  const category = findCategoryById(treeData.value, categoryId);
  return category ? getWarehouseName(category.layeringType || 0) : "";
};

// 添加目录筛选相关的数据
const categoryFilter = ref({
  keyword: "",
  layeringType: null as number | null
});

// 添加目录筛选方法
const filterTreeData = computed(() => {
  // 使用已经从API获取的树数据
  return treeData.value;
});

// 当筛选条件变化时，重新获取树数据
const handleApplyFilter = () => {
  console.log("应用筛选", categoryFilter.value);
  fetchTreeData();
};

// 重置筛选条件
const handleResetTreeFilter = () => {
  categoryFilter.value.keyword = "";
  categoryFilter.value.layeringType = null;
  console.log("重置筛选");
  fetchTreeData();
};

// 添加加载状态
const treeLoading = ref(false);

// 修改获取树数据的方法
const fetchTreeData = async () => {
  treeLoading.value = true;
  try {
    const response = (await getDataStandardPage({
      name: categoryFilter.value.keyword || undefined,
      layeringTypes: categoryFilter.value.layeringType
        ? [categoryFilter.value.layeringType]
        : undefined,
      page: 1,
      limit: 9999
    })) as any; // 临时使用 any 类型

    console.log("API 响应数据:", response);

    if (response && response.data) {
      // 直接使用返回的数据，因为它已经是树形结构
      treeData.value = response.data;
      console.log("处理后的树形结构:", treeData.value);
    } else {
      console.log("API 返回数据为空或格式不正确");
      treeData.value = []; // 确保在没有数据时清空树
    }
  } catch (error) {
    console.error("获取目录树失败:", error);
    ElMessage.error("获取目录树失败");
    treeData.value = []; // 确保在请求失败时清空树
  } finally {
    treeLoading.value = false;
  }
};

// 在组件挂载时获取数据
onMounted(() => {
  fetchWarehouseList();
  fetchTreeData();
  console.log("组件已挂载，开始获取数据");
});

// 获取所有节点ID，用于树的默认展开
const getAllNodeIds = computed(() => {
  const ids: number[] = [];
  const collectIds = (nodes: Category[]) => {
    nodes.forEach(node => {
      ids.push(node.id);
      if (node.children && node.children.length > 0) {
        collectIds(node.children);
      }
    });
  };

  collectIds(treeData.value);
  return ids;
});
</script>

<style lang="scss" scoped>
.table-detail-container {
  height: 98%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .page-header {
    margin-bottom: 20px;
    h2 {
      margin: 0;
      font-size: 20px;
      font-weight: 500;
    }
  }

  .page-content {
    flex: 1;
    display: flex;
    gap: 20px;
    min-height: 0;
    overflow: hidden;

    .table-list {
      width: 250px;
      background: #fff;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .header {
        padding: 12px 15px;
        display: flex;
        flex-direction: column;
        gap: 12px;
        border-bottom: 1px solid #eee;

        .action-buttons {
          display: flex;
          gap: 8px;

          .el-button {
            display: inline-flex;
            align-items: center;
            height: 32px;
            width: 100%;

            .icon-btn {
              font-size: 16px;
              margin-right: 4px;
            }
          }
        }

        .filter-section {
          display: flex;
          flex-direction: column;
          gap: 8px;

          .filter-actions {
            display: flex;
            gap: 8px;

            .el-button {
              flex: 1;
            }
          }
        }
      }

      .list {
        flex: 1;
        overflow-y: auto;
        padding: 10px;

        :deep(.el-tree-node__content) {
          height: 32px;
        }

        :deep(.el-tree-node__children .el-tree-node__content) {
          padding-left: 8px;
        }

        .custom-tree-node {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 14px;
          padding: 4px 8px;
          border-radius: 4px;

          .node-actions {
            display: none;
            align-items: center;
            margin-left: 8px;
            z-index: 10;
          }

          &:hover .node-actions {
            display: flex;
          }

          &.active {
            background-color: #ecf5ff;
            color: #409eff;
          }
        }
      }
    }

    .table-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-width: 0;
      overflow: hidden;

      .box-card {
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        :deep(.el-card__body) {
          flex: 1;
          display: flex;
          flex-direction: column;
          overflow: hidden;
          padding: 20px;
        }
      }

      .info-section,
      .field-section {
        background: #eef3fb;
        border-radius: 10px;
        padding: 24px;
      }

      .info-section {
        margin-bottom: 20px;

        .section-title {
          display: flex;
          align-items: center;
          font-size: 18px;
          font-weight: bold;
          color: #222;
          margin-bottom: 16px;

          &::before {
            content: "";
            display: inline-block;
            width: 4px;
            height: 22px;
            background: #409eff;
            border-radius: 2px;
            margin-right: 8px;
          }
        }

        .table-info-row {
          display: flex;
          flex-wrap: wrap;
          gap: 32px;

          .info-item {
            flex: 1;
            min-width: 200px;
            display: flex;
            align-items: center;
            margin-bottom: 12px;

            .label {
              color: #606266;
              margin-right: 8px;
              font-size: 14px;
            }

            .value {
              color: #333;
              font-size: 14px;
            }
          }
        }
      }

      .field-section {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        .section-title {
          display: flex;
          align-items: center;
          font-size: 18px;
          font-weight: bold;
          color: #222;
          margin-bottom: 16px;

          &::before {
            content: "";
            display: inline-block;
            width: 4px;
            height: 22px;
            background: #409eff;
            border-radius: 2px;
            margin-right: 8px;
          }
        }

        .filter-actions {
          margin-bottom: 16px;
        }

        .filter-form {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          gap: 12px;
          margin-bottom: 12px;
        }

        .toolbar-row {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          gap: 12px;
          margin-bottom: 12px;

          .selected-tip {
            margin-left: 16px;
            color: #666;
            font-size: 14px;
          }
        }

        .el-table {
          flex: 1;
          min-height: 200px;
          overflow-y: auto;
        }

        .pagination-container {
          display: flex;
          justify-content: flex-end;
          margin-top: 20px;
          padding: 0 10px;
        }
      }
    }
  }
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background: #f5f7fa;
  border-radius: 4px;
}

/* 表格样式增强 */
.regex-code {
  background-color: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-size: 12px;
  color: #e83e8c;
  border: 1px solid #e9ecef;
}

.text-gray-400 {
  color: #9ca3af;
  font-style: italic;
}

/* 表格行悬停效果 */
:deep(.el-table__row:hover) {
  background-color: #f8f9fa;
}

/* 表格头部样式 */
:deep(.el-table th) {
  background-color: #fafbfc;
  color: #606266;
  font-weight: 500;
}

/* 标签样式调整 */
:deep(.el-tag) {
  font-size: 12px;
  border-radius: 12px;
  padding: 0 8px;
  height: 22px;
  line-height: 20px;
}
</style>
