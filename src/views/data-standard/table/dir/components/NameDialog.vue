<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑命名标准' : '新增命名标准'"
    width="700px"
    :before-close="handleClose"
  >
    <el-form ref="formRef" :model="form" label-width="120px" :rules="formRules">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="名称" prop="name" required>
            <el-input v-model="form.name" placeholder="请输入名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="缩写" prop="shortName" required>
            <el-input v-model="form.shortName" placeholder="请输入缩写" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属目录" prop="dir" required>
            <el-tree-select
              v-model="form.dir"
              :data="catalogOptions"
              placeholder="请选择目录"
              style="width: 100%"
              :render-after-expand="false"
              :check-strictly="true"
              :props="{
                value: 'id',
                label: 'name',
                children: 'children'
              }"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="命名规则" prop="ruleType" required>
            <el-select
              v-model="form.ruleType"
              placeholder="请选择命名规则"
              style="width: 100%"
            >
              <el-option label="无限制" :value="1" />
              <el-option label="小驼峰" :value="2" />
              <el-option label="大驼峰" :value="3" />
              <el-option label="小写+下划线" :value="4" />
              <el-option label="大写+下划线" :value="5" />
              <el-option label="正则表达式" :value="6" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="最小长度" prop="minLength" required>
            <el-input-number
              v-model="form.minLength"
              :min="0"
              :max="9999"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="最大长度" prop="maxLength" required>
            <el-input-number
              v-model="form.maxLength"
              :min="1"
              :max="9999"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item
        v-if="form.ruleType === 6"
        label="正则表达式"
        prop="matchPattern"
        required
      >
        <el-input v-model="form.matchPattern" placeholder="请输入正则表达式" />
      </el-form-item>

      <el-form-item label="描述" prop="comment">
        <el-input
          v-model="form.comment"
          type="textarea"
          :rows="3"
          placeholder="请输入描述"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { ElMessage } from "element-plus";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  catalogOptions: {
    type: Array as () => Array<any>,
    default: () => []
  },
  nameData: {
    type: Object,
    default: () => ({
      name: "",
      dir: null,
      shortName: "",
      comment: "",
      minLength: 0,
      maxLength: 50,
      ruleType: 1,
      matchPattern: ""
    })
  }
});

const emit = defineEmits(["update:visible", "submit", "cancel"]);

const dialogVisible = ref(props.visible);
const form = ref({
  name: "",
  dir: null as number | null,
  shortName: "",
  comment: "",
  minLength: 0,
  maxLength: 50,
  ruleType: 1,
  matchPattern: ""
});
const formRef = ref();

// 表单验证规则
const formRules = ref({
  name: [{ required: true, message: "请输入名称", trigger: "blur" }],
  shortName: [{ required: true, message: "请输入缩写", trigger: "blur" }],
  dir: [{ required: true, message: "请选择所属目录", trigger: "change" }],
  ruleType: [{ required: true, message: "请选择命名规则", trigger: "change" }],
  minLength: [{ required: true, message: "请输入最小长度", trigger: "blur" }],
  maxLength: [{ required: true, message: "请输入最大长度", trigger: "blur" }],
  matchPattern: [
    {
      required: false, // 动态验证，在handleSubmit中处理
      message: "请输入正则表达式",
      trigger: "blur"
    }
  ]
});

// 监听visible属性变化
watch(
  () => props.visible,
  val => {
    dialogVisible.value = val;
  }
);

// 监听dialogVisible变化，同步回父组件
watch(
  () => dialogVisible.value,
  val => {
    emit("update:visible", val);
  }
);

// 监听nameData变化，更新表单
watch(
  () => props.nameData,
  val => {
    if (val) {
      form.value = {
        name: val.name || "",
        dir: val.dir || null,
        shortName: val.shortName || "",
        comment: val.comment || "",
        minLength: val.minLength || 0,
        maxLength: val.maxLength || 50,
        ruleType: val.ruleType || 1,
        matchPattern: val.matchPattern || ""
      };
    }
  },
  { deep: true, immediate: true }
);

const handleClose = () => {
  dialogVisible.value = false;
};

const handleCancel = () => {
  dialogVisible.value = false;
  emit("cancel");
};

const handleSubmit = async () => {
  // 使用Element Plus的表单验证
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    // 额外的业务验证
    if (form.value.minLength > form.value.maxLength) {
      ElMessage.error("最小长度不能大于最大长度");
      return;
    }

    // 如果选择了正则表达式规则，必须填写正则表达式
    if (form.value.ruleType === 6 && !form.value.matchPattern.trim()) {
      ElMessage.error("选择正则表达式规则时，必须填写正则表达式");
      return;
    }

    // 提交表单数据
    emit("submit", {
      ...form.value
    });

    dialogVisible.value = false;
  } catch (error) {
    console.log("表单验证失败:", error);
  }
};
</script>

<script lang="ts">
export default {
  name: "NameDialog"
};
</script>
