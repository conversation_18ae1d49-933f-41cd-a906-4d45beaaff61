<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑命名标准' : '新增命名标准'"
    width="600px"
    :before-close="handleClose"
  >
    <el-form ref="formRef" :model="form" label-width="110px">
      <el-form-item label="目录" required>
        <el-select
          v-model="form.catalog"
          placeholder="请选择目录"
          style="width: 100%"
        >
          <el-option
            v-for="item in catalogOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="中文名称" required>
        <el-input v-model="form.cnName" placeholder="中文名称" />
      </el-form-item>
      <el-form-item label="英文名称" required>
        <el-input v-model="form.enName" placeholder="英文名称" />
      </el-form-item>
      <el-form-item label="英文缩写">
        <el-input v-model="form.enAbbr" placeholder="英文缩写" />
      </el-form-item>
      <el-form-item label="备注">
        <el-input
          v-model="form.remark"
          type="textarea"
          :rows="3"
          placeholder="备注"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { ElMessage } from "element-plus";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  catalogOptions: {
    type: Array,
    default: () => []
  },
  nameData: {
    type: Object,
    default: () => ({
      catalog: "",
      cnName: "",
      enName: "",
      enAbbr: "",
      remark: ""
    })
  }
});

const emit = defineEmits(["update:visible", "submit", "cancel"]);

const dialogVisible = ref(props.visible);
const form = ref({
  catalog: "",
  cnName: "",
  enName: "",
  enAbbr: "",
  remark: ""
});
const formRef = ref();

// 监听visible属性变化
watch(
  () => props.visible,
  val => {
    dialogVisible.value = val;
  }
);

// 监听dialogVisible变化，同步回父组件
watch(
  () => dialogVisible.value,
  val => {
    emit("update:visible", val);
  }
);

// 监听nameData变化，更新表单
watch(
  () => props.nameData,
  val => {
    if (val) {
      form.value = {
        catalog: val.catalog || "",
        cnName: val.cnName || "",
        enName: val.enName || "",
        enAbbr: val.enAbbr || "",
        remark: val.remark || ""
      };
    }
  },
  { deep: true, immediate: true }
);

const handleClose = () => {
  dialogVisible.value = false;
};

const handleCancel = () => {
  dialogVisible.value = false;
  emit("cancel");
};

const handleSubmit = () => {
  // 表单验证
  if (!form.value.catalog) {
    ElMessage.error("请选择目录");
    return;
  }

  if (!form.value.cnName) {
    ElMessage.error("请输入中文名称");
    return;
  }

  if (!form.value.enName) {
    ElMessage.error("请输入英文名称");
    return;
  }

  // 提交表单
  emit("submit", {
    ...form.value
  });

  dialogVisible.value = false;
};
</script>

<script lang="ts">
export default {
  name: "NameDialog"
};
</script>
