<template>
  <div class="table-detail-container">
    <div class="page-header">
      <h2>字段标准</h2>
    </div>
    <div class="page-content">
      <!-- 左侧树结构 -->
      <div class="table-list">
        <div class="header">
          <div class="action-buttons">
            <el-button type="primary" @click="handleAddCategory"
              >新建目录</el-button
            >
          </div>
          <div class="filter-section">
            <el-input
              v-model="categoryFilter.keyword"
              placeholder="请输入目录名称"
              clearable
              style="width: 100%"
            />
            <el-select
              v-model="categoryFilter.layeringType"
              placeholder="请选择数仓"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="item in warehouseOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
            <div class="filter-actions">
              <el-button type="primary" size="small" @click="handleApplyFilter"
                >查询</el-button
              >
              <el-button size="small" @click="handleResetTreeFilter"
                >重置</el-button
              >
            </div>
          </div>
        </div>
        <div v-loading="treeLoading" class="list">
          <el-tree
            :data="filterTreeData"
            :props="defaultProps"
            node-key="id"
            :default-expanded-keys="getAllNodeIds"
            @node-click="handleNodeClick"
          >
            <template #default="{ node, data }">
              <span
                :class="[
                  'custom-tree-node',
                  { active: currentCategory === data.id }
                ]"
              >
                {{ node.label }}
                <span class="node-actions">
                  <el-link
                    type="primary"
                    style="margin-right: 8px"
                    @click.stop="handleEditCategory(data)"
                  >
                    <el-icon><Edit /></el-icon>
                  </el-link>
                  <el-link
                    type="danger"
                    @click.stop="handleDeleteCategory(data)"
                  >
                    <el-icon><Delete /></el-icon>
                  </el-link>
                </span>
              </span>
            </template>
          </el-tree>
        </div>
      </div>
      <!-- 右侧表格 -->
      <div class="table-info">
        <!-- 单个容器包含所有内容 -->
        <el-card class="box-card">
          <div v-if="!currentCategory" class="empty-state">
            <el-empty description="请选择一个字段类别" />
          </div>
          <template v-else>
            <!-- 上部分：目录基本信息 -->
            <div class="info-section">
              <div class="section-title">目录信息</div>
              <div class="table-info-row">
                <div class="info-item">
                  <span class="label">目录名称：</span>
                  <span class="value">{{
                    getCategoryName(currentCategory)
                  }}</span>
                </div>
                <div class="info-item">
                  <span class="label">所属数仓：</span>
                  <span class="value">{{
                    getCategoryWarehouse(currentCategory)
                  }}</span>
                </div>
              </div>
              <div class="table-info-row">
                <div class="info-item" style="width: 100%">
                  <span class="label">目录描述：</span>
                  <span class="value">{{
                    getCategoryDescription(currentCategory) || "暂无描述"
                  }}</span>
                </div>
              </div>
            </div>

            <!-- 下部分：筛选、添加和列表展示 -->
            <div class="field-section">
              <div class="section-title">字段标准</div>
              <div class="filter-actions">
                <el-form :inline="true" class="filter-form" @submit.prevent>
                  <el-form-item label="名称：">
                    <el-input
                      v-model="filter.cnName"
                      placeholder="请输入名称"
                      clearable
                    />
                  </el-form-item>
                  <el-form-item label="数据类型：">
                    <el-select
                      v-model="filter.dataType"
                      placeholder="请选择"
                      clearable
                      style="width: 150px"
                    >
                      <el-option label="字符串" value="string" />
                      <el-option label="数值" value="number" />
                      <el-option label="日期" value="date" />
                      <el-option label="布尔值" value="boolean" />
                      <el-option label="时间戳" value="timestamp" />
                    </el-select>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="handleFilter"
                      >查询</el-button
                    >
                    <el-button @click="handleReset">重置</el-button>
                  </el-form-item>
                </el-form>
                <div class="toolbar-row">
                  <el-button type="primary" @click="handleAddField"
                    >新增字段标准</el-button
                  >
                  <el-button
                    type="danger"
                    :disabled="!multipleSelection.length"
                    @click="handleBatchDelete"
                    >批量删除</el-button
                  >
                  <!-- <el-button @click="handleExport">导出字段标准</el-button> -->
                  <div v-if="multipleSelection.length" class="selected-tip">
                    已选 {{ multipleSelection.length }} 项
                  </div>
                </div>
              </div>
              <el-table
                :data="paginatedTableData"
                style="width: 100%"
                @selection-change="handleSelectionChange"
              >
                <el-table-column type="selection" width="50" />
                <el-table-column prop="id" label="序号" width="80" />
                <el-table-column
                  prop="name"
                  label="名称"
                  min-width="150"
                  show-overflow-tooltip
                />
                <el-table-column
                  prop="shortName"
                  label="缩写"
                  width="120"
                  show-overflow-tooltip
                />
                <el-table-column prop="dataType" label="数据类型" width="120">
                  <template #default="scope">
                    <el-tag :type="getDataTypeTagType(scope.row.dataType)">
                      {{ getDataTypeName(scope.row.dataType) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="长度限制" width="120">
                  <template #default="scope">
                    {{ scope.row.minLength }}-{{ scope.row.maxLength }}
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="hasNumberFields"
                  prop="scale"
                  label="精度"
                  width="80"
                />
                <el-table-column prop="nullable" label="是否可空" width="100">
                  <template #default="scope">
                    <el-tag :type="scope.row.nullable ? 'success' : 'danger'">
                      {{ scope.row.nullable ? "是" : "否" }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="ruleType" label="命名规则" width="120">
                  <template #default="scope">
                    <el-tag :type="getRuleTypeTagType(scope.row.ruleType)">
                      {{ getRuleTypeName(scope.row.ruleType) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="comment"
                  label="描述"
                  min-width="200"
                  show-overflow-tooltip
                >
                  <template #default="scope">
                    <span v-if="scope.row.comment">{{
                      scope.row.comment
                    }}</span>
                    <span v-else class="text-gray-400">-</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="120" fixed="right">
                  <template #default="scope">
                    <el-link
                      type="primary"
                      style="margin-right: 10px"
                      @click="handleEdit(scope.row)"
                      >编辑</el-link
                    >
                    <el-link type="danger" @click="handleDelete(scope.row)"
                      >删除</el-link
                    >
                  </template>
                </el-table-column>
              </el-table>

              <!-- 分页控件 -->
              <div class="pagination-container">
                <el-pagination
                  v-model:current-page="currentPage"
                  v-model:page-size="pageSize"
                  :page-sizes="[10, 20, 50, 100]"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="totalCount"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                />
              </div>
            </div>
          </template>
        </el-card>
      </div>
    </div>

    <!-- 新建/编辑目录弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑目录' : '新增目录'"
      width="500px"
      :before-close="handleClose"
    >
      <el-form ref="categoryFormRef" :model="categoryForm" label-width="80px">
        <el-form-item label="父级目录" required>
          <el-select
            v-model="categoryForm.parentId"
            placeholder="请选择父级目录"
            style="width: 100%"
          >
            <el-option
              v-for="item in parentOptions"
              :key="item.id"
              :label="item.label"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="所属数仓" required>
          <el-select
            v-model="categoryForm.warehouseId"
            placeholder="请选择所属数仓"
            style="width: 100%"
          >
            <el-option
              v-for="item in warehouseOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="目录名称" required>
          <el-input v-model="categoryForm.name" placeholder="目录名称" />
        </el-form-item>
        <el-form-item label="目录描述">
          <el-input
            v-model="categoryForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入目录描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitCategoryForm">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 新建/编辑字段标准弹窗 -->
    <FieldDialog
      v-model:visible="fieldDialogVisible"
      :is-edit="isFieldEdit"
      :catalog-options="catalogOptions"
      :field-data="fieldForm"
      @submit="submitFieldForm"
      @cancel="handleFieldDialogCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { Edit, Delete } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  showError,
  handleApiResponse,
  handleFormSubmit
} from "@/utils/errorHandler";
import FieldDialog from "./components/FieldDialog.vue";
import {
  getDataStandardPage,
  getFieldStandardPage,
  saveFieldStandard,
  updateFieldStandard,
  deleteFieldStandard,
  getLayeringTypeList,
  DataTypeEnum,
  RuleTypeEnum,
  type FieldStandardItem,
  type LayeringTypeItem
} from "@/api/data-standard";

// 定义目录接口类型
interface Category {
  id: number;
  name: string;
  description?: string;
  warehouseId?: number;
  children?: Category[];
}

// 数仓数据
const warehouseList = ref<LayeringTypeItem[]>([]);

// 数仓选项
const warehouseOptions = computed(() => {
  return warehouseList.value.map(item => ({
    id: item.id,
    name: item.name
  }));
});

// 获取数仓分层列表
const fetchWarehouseList = async () => {
  try {
    const response = await getLayeringTypeList();
    if (response && response.code === 0) {
      warehouseList.value = response.data || [];
    } else {
      console.error("获取数仓分层列表失败:", response?.msg);
      ElMessage.error(response?.msg || "获取数仓分层列表失败");
    }
  } catch (error) {
    console.error("获取数仓分层列表失败:", error);
    ElMessage.error("获取数仓分层列表失败");
  }
};

// mock 树结构数据
const treeData = ref<Category[]>([
  {
    id: 1,
    name: "基础字段",
    description: "系统中常用的基础字段定义",
    warehouseId: 1,
    children: [
      {
        id: 11,
        name: "用户信息",
        description: "用户相关的字段定义",
        warehouseId: 1
      },
      {
        id: 12,
        name: "订单信息",
        description: "订单相关的字段定义",
        warehouseId: 1
      }
    ]
  },
  {
    id: 2,
    name: "业务字段",
    description: "特定业务场景使用的字段定义",
    warehouseId: 2,
    children: [
      {
        id: 21,
        name: "交易信息",
        description: "交易相关的字段定义",
        warehouseId: 2
      }
    ]
  }
]);

const defaultProps = {
  children: "children",
  label: "name"
};

const currentCategory = ref<number | null>(null);

// 添加目录筛选相关的数据
const categoryFilter = ref({
  keyword: "",
  layeringType: null as number | null
});

// 添加加载状态
const treeLoading = ref(false);

// 字段标准数据
const tableData = ref<FieldStandardItem[]>([]);
const totalCount = ref(0);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);

// 由于使用API分页，直接使用tableData
const paginatedTableData = computed(() => {
  return tableData.value;
});

const handleSizeChange = (newSize: number) => {
  pageSize.value = newSize;
  currentPage.value = 1; // 重置到第一页
  fetchFieldData();
};

const handleCurrentChange = (newPage: number) => {
  currentPage.value = newPage;
  fetchFieldData();
};

// 获取目录名称
const getCategoryName = (categoryId: number) => {
  const category = findCategoryById(treeData.value, categoryId);
  return category ? category.name : "";
};

// 获取目录描述
const getCategoryDescription = (categoryId: number) => {
  const category = findCategoryById(treeData.value, categoryId);
  return category ? category.description : "";
};

// 获取数仓名称
const getWarehouseName = (warehouseId: number) => {
  const warehouse = warehouseList.value.find(item => item.id === warehouseId);
  return warehouse ? warehouse.name : "";
};

// 获取所属数仓
const getCategoryWarehouse = (categoryId: number) => {
  const category = findCategoryById(treeData.value, categoryId);
  return category ? getWarehouseName(category.warehouseId || 0) : "";
};

// 获取数据类型名称
const getDataTypeName = (dataType: number) => {
  const typeMap = {
    [DataTypeEnum.FLOAT]: "浮点数",
    [DataTypeEnum.STRING]: "字符串",
    [DataTypeEnum.DATE]: "日期",
    [DataTypeEnum.BOOLEAN]: "布尔值",
    [DataTypeEnum.INTEGER]: "整数",
    [DataTypeEnum.BIGINT]: "大整数",
    [DataTypeEnum.SERIALIZABLE]: "可序列化",
    [DataTypeEnum.BINARY]: "二进制",
    [DataTypeEnum.TIMESTAMP]: "时间戳",
    [DataTypeEnum.TIME]: "时间",
    [DataTypeEnum.UNLIMITED]: "无限制"
  };
  return typeMap[dataType] || "未知";
};

// 获取命名规则名称
const getRuleTypeName = (ruleType: number) => {
  const ruleMap = {
    [RuleTypeEnum.UNLIMITED]: "无限制",
    [RuleTypeEnum.CAMEL_CASE]: "小驼峰",
    [RuleTypeEnum.PASCAL_CASE]: "大驼峰",
    [RuleTypeEnum.SNAKE_CASE_LOWER]: "小写+下划线",
    [RuleTypeEnum.SNAKE_CASE_UPPER]: "大写+下划线"
  };
  return ruleMap[ruleType] || "未知";
};

// 获取数据类型标签类型
const getDataTypeTagType = (
  dataType: number
): "success" | "info" | "warning" | "danger" | "primary" | undefined => {
  const typeMap: Record<
    number,
    "success" | "info" | "warning" | "danger" | "primary" | undefined
  > = {
    [DataTypeEnum.FLOAT]: "warning",
    [DataTypeEnum.STRING]: "primary",
    [DataTypeEnum.DATE]: "success",
    [DataTypeEnum.BOOLEAN]: "info",
    [DataTypeEnum.INTEGER]: "warning",
    [DataTypeEnum.BIGINT]: "danger",
    [DataTypeEnum.SERIALIZABLE]: undefined,
    [DataTypeEnum.BINARY]: "danger",
    [DataTypeEnum.TIMESTAMP]: "success",
    [DataTypeEnum.TIME]: "success",
    [DataTypeEnum.UNLIMITED]: undefined
  };
  return typeMap[dataType] || undefined;
};

// 获取命名规则标签类型
const getRuleTypeTagType = (
  ruleType: number
): "success" | "info" | "warning" | "danger" | "primary" | undefined => {
  const typeMap: Record<
    number,
    "success" | "info" | "warning" | "danger" | "primary" | undefined
  > = {
    [RuleTypeEnum.UNLIMITED]: undefined,
    [RuleTypeEnum.CAMEL_CASE]: "success",
    [RuleTypeEnum.PASCAL_CASE]: "warning",
    [RuleTypeEnum.SNAKE_CASE_LOWER]: "info",
    [RuleTypeEnum.SNAKE_CASE_UPPER]: "danger"
  };
  return typeMap[ruleType] || undefined;
};

// 判断是否有数值类型的字段
const hasNumberFields = computed(() => {
  return tableData.value.some(
    item =>
      item.dataType === DataTypeEnum.FLOAT ||
      item.dataType === DataTypeEnum.INTEGER ||
      item.dataType === DataTypeEnum.BIGINT
  );
});

// 过滤搜索相关
const filter = ref({
  cnName: "", // 保持原有字段名，在API调用时映射为name
  dataType: ""
});

const multipleSelection = ref<any[]>([]);

const handleSelectionChange = (val: any[]) => {
  multipleSelection.value = val;
};

// 添加目录筛选方法
const filterTreeData = computed(() => {
  // 使用已经从API获取的树数据
  return treeData.value;
});

// 当筛选条件变化时，重新获取树数据
const handleApplyFilter = () => {
  console.log("应用筛选", categoryFilter.value);
  fetchTreeData();
};

// 重置筛选条件
const handleResetTreeFilter = () => {
  categoryFilter.value.keyword = "";
  categoryFilter.value.layeringType = null;
  console.log("重置筛选");
  fetchTreeData();
};

// 获取所有节点ID，用于树的默认展开
const getAllNodeIds = computed(() => {
  const ids: number[] = [];
  const collectIds = (nodes: Category[]) => {
    nodes.forEach(node => {
      ids.push(node.id);
      if (node.children && node.children.length > 0) {
        collectIds(node.children);
      }
    });
  };

  collectIds(treeData.value);
  return ids;
});

const handleNodeClick = (data: any) => {
  currentCategory.value = data.id;
  // 重置筛选条件
  filter.value.cnName = "";
  filter.value.dataType = "";
  multipleSelection.value = [];
  // 重置分页
  currentPage.value = 1;
  // 获取字段数据
  fetchFieldData();
};

// 获取字段标准数据
const fetchFieldData = async () => {
  if (!currentCategory.value) return;

  try {
    const response = await getFieldStandardPage({
      dir: currentCategory.value,
      name: filter.value.cnName || undefined, // cnName映射为name参数
      page: currentPage.value,
      limit: pageSize.value
    });

    if (response && response.data) {
      tableData.value = response.data.list || [];
      totalCount.value = response.data.total || 0;
    }
  } catch (error) {
    showError(error, "获取字段标准数据失败");
  }
};

// 目录弹窗相关
const dialogVisible = ref(false);
const isEdit = ref(false);
const editingCategoryId = ref<number | null>(null);
const categoryForm = ref({
  parentId: 0,
  warehouseId: 0,
  name: "",
  description: ""
});
const categoryFormRef = ref();

// 获取可选的父级目录
const parentOptions = computed(() => {
  return [
    { id: 0, label: "根目录" },
    ...treeData.value.map(item => ({
      id: item.id,
      label: item.name
    }))
  ];
});

const handleClose = () => {
  dialogVisible.value = false;
  resetForm();
  isEdit.value = false;
  editingCategoryId.value = null;
};

const resetForm = () => {
  categoryForm.value = {
    parentId: 0,
    warehouseId: 0,
    name: "",
    description: ""
  };
};

const handleAddCategory = () => {
  isEdit.value = false;
  editingCategoryId.value = null;
  resetForm();
  dialogVisible.value = true;
};

const submitCategoryForm = () => {
  // 表单验证
  if (!categoryForm.value.parentId && categoryForm.value.parentId !== 0) {
    ElMessage.error("请选择父级目录");
    return;
  }

  if (!categoryForm.value.warehouseId) {
    ElMessage.error("请选择所属数仓");
    return;
  }

  if (!categoryForm.value.name) {
    ElMessage.error("请输入目录名称");
    return;
  }

  if (isEdit.value && editingCategoryId.value !== null) {
    // 编辑目录
    const node = findCategoryById(treeData.value, editingCategoryId.value);
    if (node) {
      // 如果父目录改变，需要移动节点
      const oldParent = findParentNode(treeData.value, editingCategoryId.value);
      const newParentId = categoryForm.value.parentId;

      if (
        (oldParent && oldParent.id !== newParentId) ||
        (!oldParent && newParentId !== 0)
      ) {
        // 需要移动节点
        // 1. 从原位置删除
        if (oldParent) {
          oldParent.children = oldParent.children!.filter(
            child => child.id !== editingCategoryId.value
          );
        } else {
          treeData.value = treeData.value.filter(
            item => item.id !== editingCategoryId.value
          );
        }

        // 2. 添加到新位置
        if (newParentId === 0) {
          // 移动到根目录
          treeData.value.push(node);
        } else {
          // 移动到新父目录
          const newParent = findCategoryById(treeData.value, newParentId);
          if (newParent) {
            if (!newParent.children) newParent.children = [];
            newParent.children.push(node);
          }
        }
      }

      // 更新名称、描述和数仓ID
      node.name = categoryForm.value.name;
      node.description = categoryForm.value.description;
      node.warehouseId = categoryForm.value.warehouseId;

      ElMessage.success("目录修改成功");
    }
  } else {
    // 新增目录
    const newId = Math.floor(Math.random() * 1000) + 100;
    const newCategory: Category = {
      id: newId,
      name: categoryForm.value.name,
      description: categoryForm.value.description,
      warehouseId: categoryForm.value.warehouseId,
      children: []
    };

    if (categoryForm.value.parentId === 0) {
      // 添加到根目录
      treeData.value.push(newCategory);
    } else {
      // 添加到指定父级目录
      const parent = findCategoryById(
        treeData.value,
        categoryForm.value.parentId
      );
      if (parent) {
        if (!parent.children) parent.children = [];
        parent.children.push(newCategory);
      }
    }

    ElMessage.success("目录添加成功");
  }

  dialogVisible.value = false;
  resetForm();
  isEdit.value = false;
  editingCategoryId.value = null;
};

// 根据ID查找目录
const findCategoryById = (tree: Category[], id: number): Category | null => {
  for (const node of tree) {
    if (node.id === id) return node;
    if (node.children) {
      const found = findCategoryById(node.children, id);
      if (found) return found;
    }
  }
  return null;
};

// 查找父节点
const findParentNode = (tree: Category[], id: number): Category | null => {
  for (const node of tree) {
    if (node.children) {
      for (const child of node.children) {
        if (child.id === id) return node;
      }
      const found = findParentNode(node.children, id);
      if (found) return found;
    }
  }
  return null;
};

const handleEditCategory = (data: any) => {
  isEdit.value = true;
  editingCategoryId.value = data.id;

  // 查找父节点
  const parent = findParentNode(treeData.value, data.id);

  categoryForm.value = {
    parentId: parent ? parent.id : 0,
    warehouseId: data.warehouseId || 0,
    name: data.name,
    description: data.description || ""
  };

  dialogVisible.value = true;
};

const handleDeleteCategory = (data: any) => {
  ElMessageBox.confirm(`确定要删除目录 "${data.name}" 吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      // 查找父节点
      const parent = findParentNode(treeData.value, data.id);

      if (parent) {
        // 从父节点的children中删除
        parent.children = parent.children!.filter(item => item.id !== data.id);
      } else {
        // 从根节点删除
        treeData.value = treeData.value.filter(item => item.id !== data.id);
      }

      // 如果删除的是当前选中的节点，清空选择
      if (currentCategory.value === data.id) {
        currentCategory.value = null;
        tableData.value = [];
      }

      ElMessage.success("删除成功");
    })
    .catch(() => {
      // 取消删除
    });
};

// 字段标准弹窗相关
const fieldDialogVisible = ref(false);
const isFieldEdit = ref(false);
const editingFieldId = ref<number | null>(null);
const fieldForm = ref({
  name: "",
  comment: "",
  dir: null as number | null,
  shortName: "",
  minLength: 0,
  maxLength: 50,
  dataType: 2,
  scale: "0",
  tableMatchPattern: ".*",
  fieldMatchPattern: ".*",
  ruleType: 1,
  nullable: true,
  id: undefined as number | undefined
});

// 目录选项 - 树形结构数据
const catalogOptions = computed(() => {
  // 转换树形数据格式以适配TreeSelect组件
  const convertTreeData = (nodes: Category[]): any[] => {
    return nodes.map(node => ({
      value: node.id,
      label: node.name,
      children:
        node.children && node.children.length > 0
          ? convertTreeData(node.children)
          : undefined
    }));
  };

  return convertTreeData(treeData.value);
});

const handleFieldDialogCancel = () => {
  fieldDialogVisible.value = false;
  resetFieldForm();
  isFieldEdit.value = false;
  editingFieldId.value = null;
};

const resetFieldForm = () => {
  fieldForm.value = {
    name: "",
    comment: "",
    dir: null,
    shortName: "",
    minLength: 0,
    maxLength: 50,
    dataType: 2,
    scale: "0",
    tableMatchPattern: ".*",
    fieldMatchPattern: ".*",
    ruleType: 1,
    nullable: true,
    id: undefined
  };
};

const handleAddField = () => {
  if (!currentCategory.value) {
    ElMessage.warning("请先选择一个目录");
    return;
  }

  isFieldEdit.value = false;
  editingFieldId.value = null;

  // 重置表单并设置当前目录
  resetFieldForm();
  fieldForm.value.dir = currentCategory.value;

  fieldDialogVisible.value = true;
};

const handleEdit = (row: FieldStandardItem) => {
  isFieldEdit.value = true;
  editingFieldId.value = row.id;

  // 填充表单 - 使用新的字段结构
  fieldForm.value = {
    name: row.name,
    comment: row.comment || "",
    dir: row.dir,
    shortName: row.shortName,
    minLength: row.minLength,
    maxLength: row.maxLength,
    dataType: row.dataType,
    scale: row.scale || "0",
    tableMatchPattern: row.tableMatchPattern,
    fieldMatchPattern: row.fieldMatchPattern,
    ruleType: row.ruleType,
    nullable: row.nullable,
    id: row.id
  };

  fieldDialogVisible.value = true;
};

const submitFieldForm = async (formData: any) => {
  try {
    // 直接使用表单数据，因为现在表单结构与API一致
    const apiData = {
      name: formData.name,
      comment: formData.comment,
      dir: formData.dir,
      shortName: formData.shortName,
      minLength: formData.minLength,
      maxLength: formData.maxLength,
      dataType: formData.dataType,
      scale: formData.scale,
      tableMatchPattern: formData.tableMatchPattern,
      fieldMatchPattern: formData.fieldMatchPattern,
      ruleType: formData.ruleType,
      nullable: formData.nullable
    };

    if (isFieldEdit.value && editingFieldId.value !== null) {
      // 编辑字段
      await updateFieldStandard({
        id: editingFieldId.value,
        ...apiData
      });
      ElMessage.success("字段修改成功");
    } else {
      // 新增字段
      await saveFieldStandard(apiData);
      ElMessage.success("字段添加成功");
    }

    // 重新获取数据
    await fetchFieldData();

    fieldDialogVisible.value = false;
    resetFieldForm();
    isFieldEdit.value = false;
    editingFieldId.value = null;
  } catch (error) {
    console.error("保存字段失败:", error);
    ElMessage.error("保存字段失败");
  }
};

const handleDelete = async (row: any) => {
  if (!currentCategory.value) return;

  ElMessageBox.confirm(`确定要删除字段 "${row.name}" 吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(async () => {
      try {
        // 传递ID数组，即使只删除一个
        const response = await deleteFieldStandard([row.id]);
        if (response && response.code === 0) {
          ElMessage.success("删除成功");
          // 重新获取数据
          await fetchFieldData();
          // 清空选择
          multipleSelection.value = [];
        } else {
          showError(response, "删除字段失败");
        }
      } catch (error) {
        showError(error, "删除字段失败");
      }
    })
    .catch(() => {
      // 取消删除
    });
};

// 批量删除功能
const handleBatchDelete = async () => {
  if (!multipleSelection.value.length) {
    ElMessage.warning("请先选择要删除的字段");
    return;
  }

  const selectedNames = multipleSelection.value
    .map(item => item.name)
    .join("、");
  ElMessageBox.confirm(
    `确定要删除选中的 ${multipleSelection.value.length} 个字段吗？\n${selectedNames}`,
    "批量删除确认",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  )
    .then(async () => {
      try {
        const ids = multipleSelection.value.map(item => item.id);
        const response = await deleteFieldStandard(ids);
        if (response && response.code === 0) {
          ElMessage.success(
            `成功删除 ${multipleSelection.value.length} 个字段`
          );
          // 重新获取数据
          await fetchFieldData();
          // 清空选择
          multipleSelection.value = [];
        } else {
          showError(response, "批量删除字段失败");
        }
      } catch (error) {
        showError(error, "批量删除字段失败");
      }
    })
    .catch(() => {
      // 取消删除
    });
};

// const handleExport = () => {
//   // TODO: 导出字段标准逻辑
//   ElMessage.info("功能开发中...");
// };

const handleFilter = () => {
  // 重置分页并重新获取数据
  currentPage.value = 1;
  fetchFieldData();
};

const handleReset = () => {
  filter.value.cnName = "";
  filter.value.dataType = "";
  // 重置分页并重新获取数据
  currentPage.value = 1;
  fetchFieldData();
};

// 修改获取树数据的方法
const fetchTreeData = async () => {
  treeLoading.value = true;
  try {
    const response = (await getDataStandardPage({
      name: categoryFilter.value.keyword || undefined,
      layeringTypes: categoryFilter.value.layeringType
        ? [categoryFilter.value.layeringType]
        : undefined,
      page: 1,
      limit: 9999
    })) as any; // 临时使用 any 类型

    console.log("API 响应数据:", response);

    if (response && response.data) {
      // 直接使用返回的数据，因为它已经是树形结构
      treeData.value = response.data;
      console.log("处理后的树形结构:", treeData.value);
    } else {
      console.log("API 返回数据为空或格式不正确");
      treeData.value = []; // 确保在没有数据时清空树
    }
  } catch (error) {
    console.error("获取目录树失败:", error);
    ElMessage.error("获取目录树失败");
    treeData.value = []; // 确保在请求失败时清空树
  } finally {
    treeLoading.value = false;
  }
};

// 在组件挂载时获取数据
onMounted(() => {
  fetchWarehouseList();
  fetchTreeData();
  console.log("组件已挂载，开始获取数据");
});
</script>

<style lang="scss" scoped>
.table-detail-container {
  height: 98%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .page-header {
    margin-bottom: 20px;
    h2 {
      margin: 0;
      font-size: 20px;
      font-weight: 500;
    }
  }

  .page-content {
    flex: 1;
    display: flex;
    gap: 20px;
    min-height: 0;
    overflow: hidden;

    .table-list {
      width: 250px;
      background: #fff;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .header {
        padding: 12px 15px;
        display: flex;
        flex-direction: column;
        gap: 12px;
        border-bottom: 1px solid #eee;

        .action-buttons {
          display: flex;
          gap: 8px;

          .el-button {
            display: inline-flex;
            align-items: center;
            height: 32px;
            width: 100%;

            .icon-btn {
              font-size: 16px;
              margin-right: 4px;
            }
          }
        }

        .filter-section {
          display: flex;
          flex-direction: column;
          gap: 8px;

          .filter-actions {
            display: flex;
            gap: 8px;

            .el-button {
              flex: 1;
            }
          }
        }
      }

      .list {
        flex: 1;
        overflow-y: auto;
        padding: 10px;

        :deep(.el-tree-node__content) {
          height: 32px;
        }

        :deep(.el-tree-node__children .el-tree-node__content) {
          padding-left: 8px;
        }

        .custom-tree-node {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 14px;
          padding: 4px 8px;
          border-radius: 4px;

          .node-actions {
            display: none;
            align-items: center;
            margin-left: 8px;
            z-index: 10;
          }

          &:hover .node-actions {
            display: flex;
          }

          &.active {
            background-color: #ecf5ff;
            color: #409eff;
          }
        }
      }
    }

    .table-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-width: 0;
      overflow: hidden;

      .box-card {
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        :deep(.el-card__body) {
          flex: 1;
          display: flex;
          flex-direction: column;
          overflow: hidden;
          padding: 20px;
        }
      }

      .info-section,
      .field-section {
        background: #eef3fb;
        border-radius: 10px;
        padding: 24px;
      }

      .info-section {
        margin-bottom: 20px;

        .section-title {
          display: flex;
          align-items: center;
          font-size: 18px;
          font-weight: bold;
          color: #222;
          margin-bottom: 16px;

          &::before {
            content: "";
            display: inline-block;
            width: 4px;
            height: 22px;
            background: #409eff;
            border-radius: 2px;
            margin-right: 8px;
          }
        }

        .table-info-row {
          display: flex;
          flex-wrap: wrap;
          gap: 32px;

          .info-item {
            flex: 1;
            min-width: 200px;
            display: flex;
            align-items: center;
            margin-bottom: 12px;

            .label {
              color: #606266;
              margin-right: 8px;
              font-size: 14px;
            }

            .value {
              color: #333;
              font-size: 14px;
            }
          }
        }
      }

      .field-section {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        .section-title {
          display: flex;
          align-items: center;
          font-size: 18px;
          font-weight: bold;
          color: #222;
          margin-bottom: 16px;

          &::before {
            content: "";
            display: inline-block;
            width: 4px;
            height: 22px;
            background: #409eff;
            border-radius: 2px;
            margin-right: 8px;
          }
        }

        .filter-actions {
          margin-bottom: 16px;
        }

        .filter-form {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          gap: 12px;
          margin-bottom: 12px;
        }

        .toolbar-row {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          gap: 12px;
          margin-bottom: 12px;

          .selected-tip {
            margin-left: 16px;
            color: #666;
            font-size: 14px;
          }
        }

        .el-table {
          flex: 1;
          min-height: 200px;
          overflow-y: auto;
        }

        .pagination-container {
          display: flex;
          justify-content: flex-end;
          margin-top: 20px;
          padding: 0 10px;
        }
      }
    }
  }
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background: #f5f7fa;
  border-radius: 4px;
}

/* 表格样式增强 */
.text-gray-400 {
  color: #9ca3af;
  font-style: italic;
}

/* 表格行悬停效果 */
:deep(.el-table__row:hover) {
  background-color: #f8f9fa;
}

/* 表格头部样式 */
:deep(.el-table th) {
  background-color: #fafbfc;
  color: #606266;
  font-weight: 500;
}

/* 标签样式调整 */
:deep(.el-tag) {
  font-size: 12px;
  border-radius: 12px;
  padding: 0 8px;
  height: 22px;
  line-height: 20px;
}
</style>
