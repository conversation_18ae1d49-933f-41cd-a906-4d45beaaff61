<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑字段标准' : '新增字段标准'"
    width="700px"
    :before-close="handleClose"
  >
    <el-form ref="formRef" :model="form" label-width="120px" :rules="formRules">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="名称" prop="name" required>
            <el-input v-model="form.name" placeholder="请输入名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="缩写" prop="shortName" required>
            <el-input v-model="form.shortName" placeholder="请输入缩写" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属目录" prop="dir" required>
            <el-tree-select
              v-model="form.dir"
              :data="catalogOptions"
              :props="{ label: 'label', value: 'value', children: 'children' }"
              placeholder="请选择目录"
              style="width: 100%"
              check-strictly
              :render-after-expand="false"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="数据类型" prop="dataType" required>
            <el-select
              v-model="form.dataType"
              placeholder="请选择数据类型"
              style="width: 100%"
            >
              <el-option label="浮点数" :value="1" />
              <el-option label="字符串" :value="2" />
              <el-option label="日期" :value="3" />
              <el-option label="布尔值" :value="4" />
              <el-option label="整数" :value="5" />
              <el-option label="大整数" :value="6" />
              <el-option label="可序列化" :value="7" />
              <el-option label="二进制" :value="8" />
              <el-option label="时间戳" :value="9" />
              <el-option label="时间" :value="10" />
              <el-option label="无限制" :value="11" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="最小长度" prop="minLength" required>
            <el-input-number
              v-model="form.minLength"
              :min="0"
              :max="9999"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="最大长度" prop="maxLength" required>
            <el-input-number
              v-model="form.maxLength"
              :min="1"
              :max="9999"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row v-if="form.dataType === 1" :gutter="20">
        <el-col :span="12">
          <el-form-item label="浮点数精度" prop="scale" required>
            <el-input v-model="form.scale" placeholder="请输入精度" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="命名规则" prop="ruleType" required>
            <el-select
              v-model="form.ruleType"
              placeholder="请选择命名规则"
              style="width: 100%"
            >
              <el-option label="无限制" :value="1" />
              <el-option label="小驼峰" :value="2" />
              <el-option label="大驼峰" :value="3" />
              <el-option label="小写+下划线" :value="4" />
              <el-option label="大写+下划线" :value="5" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row v-else :gutter="20">
        <el-col :span="24">
          <el-form-item label="命名规则" prop="ruleType" required>
            <el-select
              v-model="form.ruleType"
              placeholder="请选择命名规则"
              style="width: 100%"
            >
              <el-option label="无限制" :value="1" />
              <el-option label="小驼峰" :value="2" />
              <el-option label="大驼峰" :value="3" />
              <el-option label="小写+下划线" :value="4" />
              <el-option label="大写+下划线" :value="5" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="表名匹配" prop="tableMatchPattern" required>
            <el-input
              v-model="form.tableMatchPattern"
              placeholder="请输入表名匹配正则表达式"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="字段名匹配" prop="fieldMatchPattern" required>
            <el-input
              v-model="form.fieldMatchPattern"
              placeholder="请输入字段名匹配正则表达式"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="是否可以为空" prop="nullable" required>
            <el-radio-group v-model="form.nullable">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="描述" prop="comment">
        <el-input
          v-model="form.comment"
          type="textarea"
          :rows="3"
          placeholder="请输入描述"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, watch } from "vue";
import { ElMessage } from "element-plus";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  catalogOptions: {
    type: Array as () => Array<{ value: string; label: string }>,
    default: () => []
  },
  fieldData: {
    type: Object,
    default: () => ({
      name: "",
      comment: "",
      dir: "",
      shortName: "",
      minLength: 0,
      maxLength: 50,
      dataType: 2,
      scale: "0",
      tableMatchPattern: ".*",
      fieldMatchPattern: ".*",
      ruleType: 1,
      nullable: true,
      id: undefined
    })
  }
});

const emit = defineEmits(["update:visible", "submit", "cancel"]);

const dialogVisible = ref(props.visible);
const form = ref({
  name: "",
  comment: "",
  dir: "",
  shortName: "",
  minLength: 0,
  maxLength: 50,
  dataType: 2,
  scale: "0",
  tableMatchPattern: ".*",
  fieldMatchPattern: ".*",
  ruleType: 1,
  nullable: true,
  id: undefined as number | undefined
});
const formRef = ref();

// 表单验证规则
const formRules = ref({
  name: [{ required: true, message: "请输入名称", trigger: "blur" }],
  shortName: [{ required: true, message: "请输入缩写", trigger: "blur" }],
  dir: [{ required: true, message: "请选择所属目录", trigger: "change" }],
  dataType: [{ required: true, message: "请选择数据类型", trigger: "change" }],
  minLength: [{ required: true, message: "请输入最小长度", trigger: "blur" }],
  maxLength: [{ required: true, message: "请输入最大长度", trigger: "blur" }],
  scale: [{ required: true, message: "请输入浮点数精度", trigger: "blur" }],
  tableMatchPattern: [
    { required: true, message: "请输入表名匹配正则表达式", trigger: "blur" }
  ],
  fieldMatchPattern: [
    { required: true, message: "请输入字段名匹配正则表达式", trigger: "blur" }
  ],
  ruleType: [{ required: true, message: "请选择命名规则", trigger: "change" }],
  nullable: [
    { required: true, message: "请选择是否可以为空", trigger: "change" }
  ]
});

// 监听visible属性变化
watch(
  () => props.visible,
  val => {
    dialogVisible.value = val;
  }
);

// 监听dialogVisible变化，同步回父组件
watch(
  () => dialogVisible.value,
  val => {
    emit("update:visible", val);
  }
);

// 监听fieldData变化，更新表单
watch(
  () => props.fieldData,
  val => {
    if (val) {
      form.value = {
        name: val.name || "",
        comment: val.comment || "",
        dir: val.dir || "",
        shortName: val.shortName || "",
        minLength: val.minLength || 0,
        maxLength: val.maxLength || 50,
        dataType: val.dataType || 2,
        scale: val.scale || "0",
        tableMatchPattern: val.tableMatchPattern || ".*",
        fieldMatchPattern: val.fieldMatchPattern || ".*",
        ruleType: val.ruleType || 1,
        nullable: val.nullable !== undefined ? val.nullable : true,
        id: val.id
      };
    }
  },
  { deep: true, immediate: true }
);

const handleClose = () => {
  dialogVisible.value = false;
};

const handleCancel = () => {
  dialogVisible.value = false;
  emit("cancel");
};

const handleSubmit = async () => {
  // 使用Element Plus的表单验证
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    // 额外的业务验证
    if (form.value.minLength > form.value.maxLength) {
      ElMessage.error("最小长度不能大于最大长度");
      return;
    }

    if (form.value.dataType === 1 && !form.value.scale) {
      ElMessage.error("浮点数类型必须输入精度");
      return;
    }

    // 提交表单数据
    emit("submit", {
      ...form.value
    });

    dialogVisible.value = false;
  } catch (error) {
    console.log("表单验证失败:", error);
  }
};
</script>

<script lang="ts">
export default {
  name: "FieldDialog"
};
</script>
