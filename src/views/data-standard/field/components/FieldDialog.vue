<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑字段标准' : '新增字段标准'"
    width="700px"
    :before-close="handleClose"
  >
    <el-form ref="formRef" :model="form" label-width="120px" :rules="formRules">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="名称" prop="name" required>
            <el-input v-model="form.name" placeholder="请输入名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="缩写" prop="shortName" required>
            <el-input v-model="form.shortName" placeholder="请输入缩写" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属目录" prop="dir" required>
            <el-tree-select
              v-model="form.dir"
              :data="catalogOptions"
              :props="{ label: 'label', value: 'value', children: 'children' }"
              placeholder="请选择目录"
              style="width: 100%"
              check-strictly
              :render-after-expand="false"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="数据类型" prop="dataType" required>
            <el-select
              v-model="form.dataType"
              placeholder="请选择数据类型"
              style="width: 100%"
            >
              <el-option label="浮点数" :value="1" />
              <el-option label="字符串" :value="2" />
              <el-option label="日期" :value="3" />
              <el-option label="布尔值" :value="4" />
              <el-option label="整数" :value="5" />
              <el-option label="大整数" :value="6" />
              <el-option label="可序列化" :value="7" />
              <el-option label="二进制" :value="8" />
              <el-option label="时间戳" :value="9" />
              <el-option label="时间" :value="10" />
              <el-option label="无限制" :value="11" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="最小长度" prop="minLength" required>
            <el-input-number
              v-model="form.minLength"
              :min="0"
              :max="9999"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="最大长度" prop="maxLength" required>
            <el-input-number
              v-model="form.maxLength"
              :min="1"
              :max="9999"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row v-if="form.dataType === 1" :gutter="20">
        <el-col :span="12">
          <el-form-item label="浮点数精度" prop="scale" required>
            <el-input v-model="form.scale" placeholder="请输入精度" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="命名规则" prop="ruleType" required>
            <el-select
              v-model="form.ruleType"
              placeholder="请选择命名规则"
              style="width: 100%"
            >
              <el-option label="无限制" :value="1" />
              <el-option label="小驼峰" :value="2" />
              <el-option label="大驼峰" :value="3" />
              <el-option label="小写+下划线" :value="4" />
              <el-option label="大写+下划线" :value="5" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row v-else :gutter="20">
        <el-col :span="24">
          <el-form-item label="命名规则" prop="ruleType" required>
            <el-select
              v-model="form.ruleType"
              placeholder="请选择命名规则"
              style="width: 100%"
            >
              <el-option label="无限制" :value="1" />
              <el-option label="小驼峰" :value="2" />
              <el-option label="大驼峰" :value="3" />
              <el-option label="小写+下划线" :value="4" />
              <el-option label="大写+下划线" :value="5" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="表名匹配" prop="tableMatchPattern" required>
            <el-input
              v-model="form.tableMatchPattern"
              placeholder="请输入表名匹配正则表达式"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="字段名匹配" prop="fieldMatchPattern" required>
            <el-input
              v-model="form.fieldMatchPattern"
              placeholder="请输入字段名匹配正则表达式"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="是否可以为空" prop="nullable" required>
            <el-radio-group v-model="form.nullable">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="描述" prop="comment">
        <el-input
          v-model="form.comment"
          type="textarea"
          :rows="3"
          placeholder="请输入描述"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button :disabled="submitLoading" @click="handleCancel"
          >取消</el-button
        >
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit"
          >保存</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, watch, nextTick } from "vue";
import { ElMessage } from "element-plus";
import {
  validateRegexPattern,
  validateFloatScale,
  validateLengthRange
} from "@/utils/validation";
import { RuleTypeEnum } from "@/api/data-standard";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  catalogOptions: {
    type: Array as () => Array<{ value: string; label: string }>,
    default: () => []
  },
  fieldData: {
    type: Object,
    default: () => ({
      name: "",
      comment: "",
      dir: "",
      shortName: "",
      minLength: 0,
      maxLength: 50,
      dataType: 2,
      scale: "0",
      tableMatchPattern: ".*",
      fieldMatchPattern: ".*",
      ruleType: 1,
      nullable: true,
      id: undefined
    })
  }
});

const emit = defineEmits(["update:visible", "submit", "cancel"]);

const dialogVisible = ref(props.visible);
const submitLoading = ref(false);
const formRef = ref();
const form = ref({
  name: "",
  comment: "",
  dir: "",
  shortName: "",
  minLength: 0,
  maxLength: 50,
  dataType: 2,
  scale: "0",
  tableMatchPattern: ".*",
  fieldMatchPattern: ".*",
  ruleType: 1,
  nullable: true,
  id: undefined as number | undefined
});

// 表单验证规则
const formRules = ref({
  name: [{ required: true, message: "请输入名称", trigger: "blur" }],
  shortName: [{ required: true, message: "请输入缩写", trigger: "blur" }],
  dir: [{ required: true, message: "请选择所属目录", trigger: "change" }],
  dataType: [{ required: true, message: "请选择数据类型", trigger: "change" }],
  minLength: [{ required: true, message: "请输入最小长度", trigger: "blur" }],
  maxLength: [{ required: true, message: "请输入最大长度", trigger: "blur" }],
  scale: [{ required: true, message: "请输入浮点数精度", trigger: "blur" }],
  tableMatchPattern: [
    { required: true, message: "请输入表名匹配正则表达式", trigger: "blur" }
  ],
  fieldMatchPattern: [
    { required: true, message: "请输入字段名匹配正则表达式", trigger: "blur" }
  ],
  ruleType: [{ required: true, message: "请选择命名规则", trigger: "change" }],
  nullable: [
    { required: true, message: "请选择是否可以为空", trigger: "change" }
  ]
});

// 重置表单函数
const resetForm = () => {
  form.value = {
    name: "",
    comment: "",
    dir: "",
    shortName: "",
    minLength: 0,
    maxLength: 50,
    dataType: 2,
    scale: "0",
    tableMatchPattern: ".*",
    fieldMatchPattern: ".*",
    ruleType: 1,
    nullable: true,
    id: undefined
  };
  // 清除表单验证状态
  nextTick(() => {
    formRef.value?.clearValidate();
  });
};

// 监听visible属性变化
watch(
  () => props.visible,
  val => {
    dialogVisible.value = val;
    if (val) {
      // 对话框打开时，根据是否有数据决定是编辑还是新增
      if (props.fieldData) {
        // 编辑模式，填充表单数据
        form.value = {
          name: props.fieldData.name || "",
          comment: props.fieldData.comment || "",
          dir: props.fieldData.dir || "",
          shortName: props.fieldData.shortName || "",
          minLength: props.fieldData.minLength || 0,
          maxLength: props.fieldData.maxLength || 50,
          dataType: props.fieldData.dataType || 2,
          scale: props.fieldData.scale || "0",
          tableMatchPattern: props.fieldData.tableMatchPattern || ".*",
          fieldMatchPattern: props.fieldData.fieldMatchPattern || ".*",
          ruleType: props.fieldData.ruleType || 1,
          nullable:
            props.fieldData.nullable !== undefined
              ? props.fieldData.nullable
              : true,
          id: props.fieldData.id
        };
      } else {
        // 新增模式，重置表单
        resetForm();
      }
    }
  }
);

// 监听dialogVisible变化，同步回父组件
watch(
  () => dialogVisible.value,
  val => {
    emit("update:visible", val);
  }
);

const handleClose = () => {
  dialogVisible.value = false;
};

const handleCancel = () => {
  dialogVisible.value = false;
  emit("cancel");
};

const handleSubmit = async () => {
  // 使用Element Plus的表单验证
  if (!formRef.value) return;

  try {
    submitLoading.value = true;
    await formRef.value.validate();

    // 额外的业务验证
    const lengthValidation = validateLengthRange(
      form.value.minLength,
      form.value.maxLength
    );
    if (!lengthValidation.valid) {
      ElMessage.error(lengthValidation.message);
      return;
    }

    // 浮点数精度验证
    if (form.value.dataType === 1) {
      const scaleValidation = validateFloatScale(form.value.scale);
      if (!scaleValidation.valid) {
        ElMessage.error(scaleValidation.message);
        return;
      }
    }

    // 正则表达式验证
    if (form.value.tableMatchPattern) {
      const tablePatternValidation = validateRegexPattern(
        form.value.tableMatchPattern
      );
      if (!tablePatternValidation.valid) {
        ElMessage.error(`表名匹配模式错误：${tablePatternValidation.message}`);
        return;
      }
    }

    if (form.value.fieldMatchPattern) {
      const fieldPatternValidation = validateRegexPattern(
        form.value.fieldMatchPattern
      );
      if (!fieldPatternValidation.valid) {
        ElMessage.error(
          `字段名匹配模式错误：${fieldPatternValidation.message}`
        );
        return;
      }
    }

    // 提交表单数据
    emit("submit", {
      ...form.value
    });

    dialogVisible.value = false;
  } catch (error) {
    console.log("表单验证失败:", error);
  } finally {
    submitLoading.value = false;
  }
};
</script>

<script lang="ts">
export default {
  name: "FieldDialog"
};
</script>
