<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑字段标准' : '新增字段标准'"
    width="600px"
    :before-close="handleClose"
  >
    <el-form ref="formRef" :model="form" label-width="110px">
      <el-form-item label="目录" required>
        <el-select
          v-model="form.catalog"
          placeholder="请选择目录"
          style="width: 100%"
        >
          <el-option
            v-for="item in catalogOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="中文名称" required>
        <el-input v-model="form.cnName" placeholder="中文名称" />
      </el-form-item>
      <el-form-item label="英文名称" required>
        <el-input v-model="form.enName" placeholder="英文名称" />
      </el-form-item>
      <el-form-item label="英文缩写">
        <el-input v-model="form.enAbbr" placeholder="英文缩写" />
      </el-form-item>
      <el-form-item label="数据类型" required>
        <el-select
          v-model="form.dataType"
          placeholder="请选择数据类型"
          style="width: 100%"
        >
          <el-option label="字符串" value="string" />
          <el-option label="数值" value="number" />
          <el-option label="日期" value="date" />
          <el-option label="布尔值" value="boolean" />
          <el-option label="时间戳" value="timestamp" />
        </el-select>
      </el-form-item>
      <el-form-item
        label="长度"
        v-if="['string', 'number'].includes(form.dataType)"
      >
        <el-input-number v-model="form.length" :min="1" :max="4000" />
      </el-form-item>
      <el-form-item label="精度" v-if="form.dataType === 'number'">
        <el-input-number v-model="form.precision" :min="0" :max="10" />
      </el-form-item>
      <el-form-item label="非空">
        <el-checkbox v-model="form.required" />
      </el-form-item>
      <el-form-item label="默认值">
        <el-input v-model="form.defaultValue" placeholder="默认值" />
      </el-form-item>
      <el-form-item label="引用权威标准">
        <el-select
          v-model="form.standardRef"
          placeholder="请选择引用权威标准"
          style="width: 100%"
        >
          <el-option label="国家标准" value="national" />
          <el-option label="行业标准" value="industry" />
          <el-option label="企业标准" value="enterprise" />
        </el-select>
      </el-form-item>
      <el-form-item label="业务定义">
        <el-input
          v-model="form.businessDef"
          type="textarea"
          :rows="3"
          placeholder="业务定义"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, watch } from "vue";
import { ElMessage } from "element-plus";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  catalogOptions: {
    type: Array,
    default: () => []
  },
  fieldData: {
    type: Object,
    default: () => ({
      catalog: "",
      cnName: "",
      enName: "",
      enAbbr: "",
      dataType: "string",
      length: 50,
      precision: 0,
      required: false,
      defaultValue: "",
      standardRef: "",
      businessDef: ""
    })
  }
});

const emit = defineEmits(["update:visible", "submit", "cancel"]);

const dialogVisible = ref(props.visible);
const form = ref({
  catalog: "",
  cnName: "",
  enName: "",
  enAbbr: "",
  dataType: "string",
  length: 50,
  precision: 0,
  required: false,
  defaultValue: "",
  standardRef: "",
  businessDef: ""
});
const formRef = ref();

// 监听visible属性变化
watch(
  () => props.visible,
  val => {
    dialogVisible.value = val;
  }
);

// 监听dialogVisible变化，同步回父组件
watch(
  () => dialogVisible.value,
  val => {
    emit("update:visible", val);
  }
);

// 监听fieldData变化，更新表单
watch(
  () => props.fieldData,
  val => {
    if (val) {
      form.value = {
        catalog: val.catalog || "",
        cnName: val.cnName || val.fieldName || "",
        enName: val.enName || "",
        enAbbr: val.enAbbr || "",
        dataType: val.dataType || val.fieldType || "string",
        length: val.length || 50,
        precision: val.precision || 0,
        required: val.required || false,
        defaultValue: val.defaultValue || "",
        standardRef: val.standardRef || "",
        businessDef: val.businessDef || val.description || ""
      };
    }
  },
  { deep: true, immediate: true }
);

const handleClose = () => {
  dialogVisible.value = false;
};

const handleCancel = () => {
  dialogVisible.value = false;
  emit("cancel");
};

const handleSubmit = () => {
  // 表单验证
  if (!form.value.catalog) {
    ElMessage.error("请选择目录");
    return;
  }

  if (!form.value.cnName) {
    ElMessage.error("请输入中文名称");
    return;
  }

  if (!form.value.enName) {
    ElMessage.error("请输入英文名称");
    return;
  }

  if (!form.value.dataType) {
    ElMessage.error("请选择数据类型");
    return;
  }

  if (
    (form.value.dataType === "string" || form.value.dataType === "number") &&
    !form.value.length
  ) {
    ElMessage.error("请输入字段长度");
    return;
  }

  // 提交表单
  emit("submit", {
    ...form.value,
    // 兼容原有字段
    fieldName: form.value.cnName,
    fieldType: form.value.dataType,
    description: form.value.businessDef
  });

  dialogVisible.value = false;
};
</script>

<script lang="ts">
export default {
  name: "FieldDialog"
};
</script>
