export default {
  path: "/data-warehouse",
  redirect: "/data-warehouse/table-detail",
  meta: {
    icon: "ri:information-line",
    showLink: false,
    title: "数仓表详情",
    rank: 9
  },
  children: [
    {
      path: "/data-warehouse/table-detail",
      component: () => import("@/views/data-warehouse/tables/detail.vue"),
      meta: {
        title: "表详情",
        activePath: "/data-warehouse"
      }
    }
  ]
} satisfies RouteConfigsTable;
