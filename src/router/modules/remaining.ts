/*
 * @Author: z<PERSON><PERSON><PERSON>
 * @Date: 2025-04-25 09:37:21
 * @LastEditors: zhangzao
 * @LastEditTime: 2025-05-07 09:23:30
 * @Description: 请填写简介
 * @FilePath: /big_data/src/router/modules/remaining.ts
 */
const Layout = () => import("@/layout/index.vue");

export default [
  {
    path: "/login",
    name: "Login",
    component: () => import("@/views/login/index.vue"),
    meta: {
      title: "登录",
      showLink: false,
      rank: 101
    }
  },
  {
    path: "/redirect",
    component: Layout,
    meta: {
      title: "加载中...",
      showLink: false,
      rank: 102
    },
    children: [
      {
        path: "/redirect/:path(.*)",
        name: "Redirect",
        component: () => import("@/layout/redirect.vue")
      }
    ]
  }
] satisfies Array<RouteConfigsTable>;
