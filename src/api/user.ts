import { http } from "@/utils/http";
import type { UserInfo } from "./types/user";

export type UserResult = {
  code: number;
  msg: string;
  data: {
    /** `token` */
    access_token: string;
  };
};

export type RefreshTokenResult = {
  success: boolean;
  data: {
    /** `token` */
    accessToken: string;
    /** 用于调用刷新`accessToken`的接口时所需的`token` */
    refreshToken: string;
    /** `accessToken`的过期时间（格式'xxxx/xx/xx xx:xx:xx'） */
    expires: Date;
  };
};

export type LoginParams = {
  username: string;
  password: string;
  key: string;
  captcha: string;
};

export type MenuItem = {
  id: number;
  pid: number;
  children: MenuItem[];
  name: string;
  url: string;
  type: number;
  openStyle: number;
  icon: string;
  authority: string;
  sort: number;
  createTime: string;
  parentName: string | null;
};

export type MenuResult = {
  code: number;
  msg: string;
  data: MenuItem[];
};

export type UserInfoResult = {
  code: number;
  msg: string;
  data: UserInfo;
};

/** 登录 */
export const getLogin = (data: LoginParams) => {
  return http.request<UserResult>("post", "/sys/auth/login", { data });
};

/** 刷新`token` */
export const refreshTokenApi = (data?: object) => {
  return http.request<RefreshTokenResult>("post", "/refresh-token", { data });
};

/** 获取动态路由 */
export const getMenuNav = () => {
  return http.request<MenuResult>("get", "/sys/menu/nav");
};

/** 获取用户信息 */
export function getUserInfo() {
  return http.request<UserInfoResult>("get", "/sys/user/info");
}
