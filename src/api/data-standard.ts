/*
 * @Author: z<PERSON><PERSON><PERSON>
 * @Date: 2025-05-09 10:00:00
 * @LastEditors: zhangzao
 * @LastEditTime: 2025-06-24 14:12:01
 * @Description: 数据标准目录管理接口
 */
import { http } from "@/utils/http";

/** 标准目录项 */
export interface DataStandardItem {
  id: number;
  name: string;
  englishName: string;
  description?: string;
  parentId?: number;
  layeringType?: number;
  children?: DataStandardItem[];
  // 其他字段按实际接口补充
}

/** 分页响应 */
export interface DataStandardPageResponse {
  total: number;
  list: DataStandardItem[];
}

/**
 * 获取标准目录分页列表
 * @param params 查询参数
 */
export const getDataStandardPage = (params: {
  name?: string;
  englishName?: string;
  layeringTypes?: number[];
  page: number;
  limit: number;
}) => {
  return http.request<DataStandardPageResponse>(
    "get",
    "/data-integrate/data-standard/dir/page",
    { params }
  );
};

/** 新建或编辑目录 */
export const saveDataStandard = (data: {
  name: string;
  englishName: string;
  englishShortName: string;
  comment: string;
  layeringType: number;
  pid: number;
}) => {
  return http.request<any>("post", "/data-integrate/data-standard/dir/save", {
    data
  });
};

/** 获取目录详情 */
export const getDataStandardDetail = (id: number) => {
  return http.request<any>("get", `/data-integrate/data-standard/dir/${id}`);
};

/** 更新目录 */
export const updateDataStandard = (data: {
  id: number;
  name: string;
  englishName: string;
  englishShortName: string;
  comment: string;
  layeringType: number;
  pid: number;
}) => {
  return http.request<any>("post", "/data-integrate/data-standard/dir/update", {
    data
  });
};

/** 批量删除目录 */
export const deleteDataStandard = (ids: number[]) => {
  return http.request<any>("delete", "/data-integrate/data-standard/dir", {
    data: ids
  });
};

// ==================== 字段标准相关接口 ====================

/** 数据类型枚举 */
export enum DataTypeEnum {
  FLOAT = 1, // 浮点数
  STRING = 2, // 字符串
  DATE = 3, // 日期
  BOOLEAN = 4, // 布尔值
  INTEGER = 5, // 整数
  BIGINT = 6, // 大整数
  SERIALIZABLE = 7, // 可序列化
  BINARY = 8, // 二进制
  TIMESTAMP = 9, // 时间戳
  TIME = 10, // 时间
  UNLIMITED = 11 // 无限制
}

/** 命名规则枚举 */
export enum RuleTypeEnum {
  UNLIMITED = 1, // 无限制
  CAMEL_CASE = 2, // 小驼峰
  PASCAL_CASE = 3, // 大驼峰
  SNAKE_CASE_LOWER = 4, // 小写+下划线
  SNAKE_CASE_UPPER = 5 // 大写+下划线
}

/** 字段标准项 */
export interface FieldStandardItem {
  id?: number;
  name: string; // 名称
  comment: string; // 描述
  dir: string; // 所属目录
  shortName: string; // 缩写
  minLength: number; // 最小长度
  maxLength: number; // 最大长度
  dataType: number; // 数据类型 浮点数 1 字符串 2 日期 3 布尔值 4 整数 5 大整数 6 可序列化 7 二进制 8 时间戳 9 时间 10 无限制 11
  scale: string; // 浮点数数据精度，数据类型为1的时候才有
  tableMatchPattern: string; // 表名匹配正则表达式
  fieldMatchPattern: string; // 字段名匹配正则表达式
  ruleType: number; // 命名规则 1 无限制 2 小驼峰 3 大驼峰 4 小写+下划线 5 大写+下划线
  nullable: boolean; // 是否可以为空
  createTime?: number;
  updateTime?: number;
}

/** 字段标准分页响应 */
export interface FieldStandardPageResponse {
  code: number;
  msg: string;
  data: {
    total: number;
    list: FieldStandardItem[];
  };
}

/**
 * 获取字段标准分页列表
 * @param params 查询参数
 */
export const getFieldStandardPage = (params: {
  name?: string; // 名称模糊搜索
  dir?: number; // 所属目录ID
  page: number;
  limit: number;
}) => {
  return http.request<FieldStandardPageResponse>(
    "get",
    "/data-integrate/data-standard/table/field/page",
    { params }
  );
};

/**
 * 新增字段标准
 * @param data 字段标准数据
 */
export const saveFieldStandard = (data: {
  name: string;
  comment: string;
  dir: string;
  shortName: string;
  minLength: number;
  maxLength: number;
  dataType: number;
  scale: string;
  tableMatchPattern: string;
  fieldMatchPattern: string;
  ruleType: number;
  nullable: boolean;
}) => {
  return http.request<any>(
    "post",
    "/data-integrate/data-standard/table/field/save",
    {
      data
    }
  );
};

/**
 * 获取字段标准详情
 * @param id 字段标准ID
 */
export const getFieldStandardDetail = (id: number) => {
  return http.request<FieldStandardItem>(
    "get",
    `/data-integrate/data-standard/table/field/${id}`
  );
};

/**
 * 更新字段标准
 * @param data 字段标准数据
 */
export const updateFieldStandard = (data: {
  id: number;
  name: string;
  comment: string;
  dir: string;
  shortName: string;
  minLength: number;
  maxLength: number;
  dataType: number;
  scale: string;
  tableMatchPattern: string;
  fieldMatchPattern: string;
  ruleType: number;
  nullable: boolean;
}) => {
  return http.request<any>(
    "post",
    "/data-integrate/data-standard/table/field/update",
    {
      data
    }
  );
};

/**
 * 删除字段标准
 * @param id 字段标准ID
 */
export const deleteFieldStandard = (id: number) => {
  return http.request<{
    code: number;
    msg: string;
    data?: any;
  }>("delete", `/data-integrate/data-standard/table/field/${id}`);
};

// ==================== 数仓分层相关接口 ====================

/** 数仓分层项 */
export interface LayeringTypeItem {
  id: number;
  name: string;
  shortName: string;
  description?: string;
}

/**
 * 获取数仓分层列表
 */
export const getLayeringTypeList = (params?: {
  name?: string;
  layerId?: string | number;
}) => {
  return http.request<{
    code: number;
    msg: string;
    data: LayeringTypeItem[];
  }>("get", "/data-integrate/data-warehouse/list", { params });
};
