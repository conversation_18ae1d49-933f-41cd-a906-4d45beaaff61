/*
 * @Author: z<PERSON><PERSON><PERSON>
 * @Date: 2025-04-28 10:31:50
 * @LastEditors: zhangzao
 * @LastEditTime: 2025-04-28 14:31:09
 * @Description: 请填写简介
 * @FilePath: /big_data/src/api/database.ts
 */
import { http } from "@/utils/http";
import type {
  DatabaseInfo,
  DatabaseListResult,
  DatabaseResult
} from "./types/database";

/** 新增数据源 */
export function addDatabase(data: Omit<DatabaseInfo, "id">) {
  return http.request("post", "/data-integrate/database/add", { data });
}

/** 获取数据源列表 */
export const getDatabaseList = (params?: any) => {
  return http.request<any>("get", "/data-integrate/database", { params });
};

/** 更新数据源 */
export const updateDatabase = (id: string | number, data: DatabaseInfo) => {
  return http.request<any>("put", `/data-integrate/database`, { data });
};

/** 删除数据源 */
export const deleteDatabase = (id: string | number) => {
  return http.request<any>("delete", "/data-integrate/database", {
    data: [id]
  });
};

/** 获取数据源详情 */
export const getDatabaseDetail = (id: string | number) => {
  return http.request<any>("get", `/data-integrate/database/${id}`);
};

/** 分页获取数据源列表 */
export const getDatabasePageList = (params?: any) => {
  return http.request<any>("get", "/data-integrate/database/page", { params });
};

/** 测试数据源连通性 */
export const testDatabaseConnection = (data: any) => {
  return http.request<any>("post", "/data-integrate/database/test-online", {
    data
  });
};

/** 批量删除数据源 */
export const batchDeleteDatabase = (ids: number[] | string[]) => {
  return http.request<any>("delete", "/data-integrate/database", {
    data: ids
  });
};

/** 导出数据源 */
export function exportDatabase(ids: number[]) {
  return http.request<BlobPart>("post", "/data-integrate/database/export", {
    data: ids,
    responseType: "blob"
  });
}
