/*
 * @Author: z<PERSON><PERSON><PERSON>
 * @Date: 2025-04-28 15:31:50
 * @LastEditors: zhangzao
 * @LastEditTime: 2025-05-09 09:55:27
 * @Description: 数据仓库分层管理接口
 */
import { http } from "@/utils/http";
import type { Layer } from "@/types/data-warehouse";

interface ApiResponse<T> {
  code: number;
  msg: string;
  data: T;
}

/** 获取数据仓库分层列表 */
export const getLayerList = (params?: { layerId?: string | number }) => {
  return http.request<ApiResponse<Layer[]>>(
    "get",
    "/data-integrate/data-warehouse/list",
    { params }
  );
};

/** 新增数据仓库分层 */
export const saveLayer = (data: {
  name: string;
  shortName: string;
  ownPerson: number;
  ownPersonName: string;
}) => {
  return http.request<ApiResponse<void>>(
    "post",
    "/data-integrate/data-warehouse/save",
    {
      data
    }
  );
};

/** 新增数据域 */
export const saveDomain = (data: {
  name: string;
  shortName: string;
  ownPerson: number;
  ownPersonName: string;
  layeringType: number;
}) => {
  return http.request<ApiResponse<void>>(
    "post",
    "/data-integrate/data-warehouse/domain/save",
    {
      data
    }
  );
};

/** 获取用户列表 */
export interface UserInfo {
  id: number;
  username: string;
  realName: string;
  avatar: string;
  gender: number;
  email: string;
  mobile: string;
  orgId: number | null;
  status: number;
  roleIdList: number[] | null;
  postIdList: number[] | null;
  superAdmin: number;
  orgName: string | null;
  createTime: string;
}

export interface UserListResponse {
  total: number;
  list: UserInfo[];
}

export const getUserList = (params: { page: number; limit: number }) => {
  return http.request<ApiResponse<UserListResponse>>("get", "/sys/user/page", {
    params
  });
};

/** 删除数据仓库分层 */
export const deleteLayer = (ids: number[]) => {
  return http.request<ApiResponse<void>>(
    "delete",
    "/data-integrate/data-warehouse",
    {
      data: ids
    }
  );
};

/** 更新数据仓库分层 */
export const updateLayer = (data: {
  id: number;
  name: string;
  shortName: string;
  ownPerson: number;
  ownPersonName: string;
  layeringType: number;
}) => {
  return http.request<ApiResponse<void>>(
    "post",
    "/data-integrate/data-warehouse/update",
    {
      data
    }
  );
};

/** 更新数据域 */
export const updateDomain = (data: {
  id: number;
  name: string;
  shortName: string;
  ownPerson: number;
  ownPersonName: string;
  layeringType: number;
}) => {
  return http.request<ApiResponse<void>>(
    "post",
    "/data-integrate/data-warehouse/domain/update",
    {
      data
    }
  );
};

/** 数据域详情接口 */
export interface DomainDetail {
  id: number;
  name: string;
  description?: string;
}

/** 获取数据域详情 */
export const getDomainDetail = (id: string | number) => {
  return http.request<ApiResponse<DomainDetail>>(
    "get",
    `/data-integrate/data-warehouse/domain/${id}`
  );
};

/** 表信息接口 */
export interface TableInfo {
  tableName: string; // 表名
  tableCn: string; // 表描述
  columns: TableColumn[]; // 表列信息
}

/** 表列表项接口 */
export interface TableListItem {
  id: number;
  tableInfo: TableInfo;
}

/** 数据域表信息 */
export interface DomainTableInfo {
  domainId: number;
  domainName: string;
  tableInfos: TableListItem[];
}

/** 分层表信息 */
export interface LayerTableInfo {
  layerId: number;
  layerName: string;
  layeringDomainTableInfos: DomainTableInfo[];
}

/** 获取表列表 */
export const getTableList = (params: {
  layerId?: string | number;
  domainId?: string | number;
}) => {
  return http.request<ApiResponse<LayerTableInfo>>(
    "get",
    `/data-integrate/data-warehouse/table/list`,
    {
      params
    }
  );
};

// 表相关接口类型定义
export interface TableColumn {
  name: string; // 列名
  comment: string; // 列描述
  fieldType: number; // 数据类型
  fieldLength: number; // 长度
  sacle: number; // 小数位数
  nullable: number; // 是否可空 0-否 1-是
  pk: number; // 主键 0-否 1-是
}

export interface SaveTableRequest {
  domainId: number; // 所属分层id
  tableInfo: TableInfo; // 使用相同的 TableInfo 接口
}

// 保存表
export function saveTable(data: SaveTableRequest) {
  return http.request<ApiResponse<void>>(
    "post",
    "/data-integrate/data-warehouse/table/save",
    {
      data
    }
  );
}

// 删除表（支持批量）
export function deleteTable(tableIds: number[]) {
  return http.request<ApiResponse<void>>(
    "delete",
    "/data-integrate/data-warehouse/table",
    {
      data: tableIds
    }
  );
}

/** 添加菜单层级参数接口 */
export interface AddMenuLayerParams {
  layeringId?: number; // 分层ID
  domainId?: number; // 数据域ID，添加数据域时必填
  layeringName: string; // 分层名称
  domainName?: string; // 数据域名称，添加数据域时必填
  menuType?: number; // 菜单类型：1-分层 2-数据域
  operation: 1 | 2; // 操作类型：1-添加 2-删除
}

/** 添加菜单层级 */
export function addMenuLayer(data: AddMenuLayerParams) {
  return http.request<ApiResponse<void>>("post", "/sys/menu/layer", {
    data
  });
}

/** 删除数据域 */
export function deleteDomain(ids: number[]) {
  return http.request<ApiResponse<void>>(
    "delete",
    "/data-integrate/data-warehouse/domain",
    {
      data: ids
    }
  );
}

/** 分层信息接口 */
export interface Layer {
  id: number;
  name: string;
  shortName: string;
  ownPerson: number;
  ownPersonName: string;
  domains?: {
    id: number;
    name: string;
    description?: string;
    ownPerson: number;
    ownPersonName: string;
    shortName: string;
  }[];
}

// 更新表
export function updateTable(data: SaveTableRequest & { id: number }) {
  return http.request<ApiResponse<void>>(
    "post",
    "/data-integrate/data-warehouse/table/update",
    {
      data
    }
  );
}
