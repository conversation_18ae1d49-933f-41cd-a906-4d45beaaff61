/** 用户信息接口返回结果 */
export interface UserInfoResult {
  code: number;
  msg: string;
  data: UserInfo;
}

/** 用户信息 */
export interface UserInfo {
  /** 用户ID */
  id: number;
  /** 用户名 */
  username: string;
  /** 真实姓名 */
  realName: string;
  /** 头像 */
  avatar: string;
  /** 性别 0-女 1-男 */
  gender: number;
  /** 邮箱 */
  email: string;
  /** 手机号 */
  mobile: string;
  /** 机构ID */
  orgId: number | null;
  /** 状态 */
  status: number;
  /** 角色ID列表 */
  roleIdList: number[] | null;
  /** 岗位ID列表 */
  postIdList: number[] | null;
  /** 是否超级管理员 */
  superAdmin: number;
  /** 机构名称 */
  orgName: string | null;
  /** 创建时间 */
  createTime: string | null;
}
