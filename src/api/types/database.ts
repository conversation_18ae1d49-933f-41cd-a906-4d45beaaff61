/** 数据源信息 */
export interface DatabaseInfo {
  /** ID */
  id?: number;
  /** 名称 */
  name: string;
  /** 数据库类型 */
  databaseType: string;
  /** 主机ip */
  databaseIp: string;
  /** 端口 */
  databasePort: string;
  /** 库名 */
  databaseName: string;
  /** Schema */
  databaseSchema: string;
  /** 用户名 */
  userName: string;
  /** 密码 */
  password: string;
  /** jdbc连接 */
  jdbcUrl: string;
  /** 描述 */
  description: string;
  /** 集群模式 1 单点 2 集群 */
  clusterMode: string;
  /** 创建人 */
  creatorName?: string;
  /** 创建时间 */
  createTime?: string;
  /** 修改人 */
  updaterName?: string;
  /** 修改时间 */
  updateTime?: string;
}
