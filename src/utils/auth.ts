import Cookies from "js-cookie";
import { useUserStoreHook } from "@/store/modules/user";
import { storageLocal, isString, isIncludeAllChildren } from "@pureadmin/utils";

export interface DataInfo<T> {
  /** token */
  accessToken: string;
  /** `accessToken`的过期时间（时间戳） */
  expires: T;
  /** 用于调用刷新accessToken的接口时所需的token */
  refreshToken: string;
  /** 头像 */
  avatar?: string;
  /** 用户名 */
  username?: string;
  /** 昵称 */
  nickname?: string;
  /** 当前登录用户的角色 */
  roles?: Array<string>;
  /** 当前登录用户的按钮级别权限 */
  permissions?: Array<string>;
  /** 新格式的token */
  access_token?: string;
  /** 用户信息 */
  user?: any;
}

export const userKey = "user-info";
export const TokenKey = "authorized-token";
/**
 * 通过`multiple-tabs`是否在`cookie`中，判断用户是否已经登录系统，
 * 从而支持多标签页打开已经登录的系统后无需再登录。
 * 浏览器完全关闭后`multiple-tabs`将自动从`cookie`中销毁，
 * 再次打开浏览器需要重新登录系统
 * */
export const multipleTabsKey = "multiple-tabs";

/** 获取`token` */
export function getToken(): DataInfo<number> | null | string {
  try {
    // 首先尝试从 Cookie 中获取
    if (Cookies.get(TokenKey)) {
      const tokenData = JSON.parse(Cookies.get(TokenKey));
      console.log("从Cookie获取到token:", tokenData);
      return tokenData;
    }

    // 如果 Cookie 中没有，尝试从 localStorage 获取
    const tokenFromStorage = storageLocal().getItem(
      userKey
    ) as DataInfo<number>;
    if (tokenFromStorage) {
      console.log("从localStorage获取到token:", tokenFromStorage);
      return tokenFromStorage;
    }

    const Token = localStorage.getItem("Authorization");
    if (Token) {
      console.log("从localStorage获取到token:", Token);
      return Token;
    }

    console.log("没有找到token");
    return null;
  } catch (error) {
    console.error("获取token时出错:", error);
    return null;
  }
}

/**
 * @description 设置`token`以及一些必要信息并采用无感刷新`token`方案
 * 无感刷新：后端返回`accessToken`（访问接口使用的`token`）、`refreshToken`（用于调用刷新`accessToken`的接口时所需的`token`，`refreshToken`的过期时间（比如30天）应大于`accessToken`的过期时间（比如2小时））、`expires`（`accessToken`的过期时间）
 * 将`accessToken`、`expires`、`refreshToken`这三条信息放在key值为authorized-token的cookie里（过期自动销毁）
 * 将`avatar`、`username`、`nickname`、`roles`、`permissions`、`refreshToken`、`expires`这七条信息放在key值为`user-info`的localStorage里（利用`multipleTabsKey`当浏览器完全关闭后自动销毁）
 */
export function setToken(data: DataInfo<any>) {
  // 处理不同格式的token
  const token =
    typeof data === "string" ? data : data.accessToken || data.access_token;

  if (token) {
    // 保存token
    Cookies.set(TokenKey, token);
    localStorage.setItem(TokenKey, token);

    // 保存用户信息
    if (typeof data === "object" && data.user) {
      localStorage.setItem(userKey, JSON.stringify(data.user));
    }

    return true;
  }
  return false;
}

/** 删除`token`以及key值为`user-info`的localStorage信息 */
export function removeToken() {
  Cookies.remove(TokenKey);
  Cookies.remove(multipleTabsKey);
  localStorage.removeItem(TokenKey);
  localStorage.removeItem(userKey);
  localStorage.removeItem("Authorization");
}

/** 格式化token（jwt格式） */
export const formatToken = (token: string): string => {
  return `Bearer ${token}`;
};

/** 是否有按钮级别的权限（根据登录接口返回的`permissions`字段进行判断）*/
export const hasPerms = (value: string | Array<string>): boolean => {
  if (!value) return false;
  const allPerms = "*:*:*";
  const { permissions } = useUserStoreHook();
  if (!permissions) return false;
  if (permissions.length === 1 && permissions[0] === allPerms) return true;
  const isAuths = isString(value)
    ? permissions.includes(value)
    : isIncludeAllChildren(value, permissions);
  return isAuths ? true : false;
};
