import Axios, {
  type AxiosRequestConfig,
  type CustomParamsSerializer
} from "axios";
import { stringify } from "qs";
import NProgress from "../progress";
import { getToken, removeToken } from "@/utils/auth";
import type { RequestMethods, PureHttpRequestConfig } from "./types.d";
import router from "@/router";

// 创建Axios实例
const instance = Axios.create({
  timeout: 10000,
  baseURL: "/api",
  withCredentials: false,
  headers: {
    Accept: "application/json, text/plain, */*",
    "Content-Type": "application/json",
    "X-Requested-With": "XMLHttpRequest"
  },
  paramsSerializer: {
    serialize: stringify as unknown as CustomParamsSerializer
  }
});

// 定义白名单
const WhiteList = [
  "/refreshToken",
  "/login",
  "/sys/auth/login",
  "/sys/auth/captcha"
];

// 请求拦截器
instance.interceptors.request.use(
  config => {
    // 开启进度条动画
    NProgress.start();

    // 确保不发送 Cookie
    config.withCredentials = false;

    // 优先判断post/get等方法是否传入回调，否则执行初始化设置等回调
    if (typeof config.beforeRequestCallback === "function") {
      config.beforeRequestCallback(config);
      return config;
    }
    if (WhiteList.some(v => config.url.includes(v))) return config;
    const token = getToken();
    if (token) {
      config.headers["Authorization"] = `${token}`;
    }
    console.log("config", config);
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 响应拦截器
instance.interceptors.response.use(
  response => {
    // 关闭进度条动画
    NProgress.done();

    // 处理401状态码
    if (response.data.code === 401) {
      removeToken();
      router.push("/login");
      return new Promise(() => {}); // 返回一个永远pending的promise，阻止后续操作
    }

    return response.data;
  },
  error => {
    // 关闭进度条动画
    NProgress.done();

    // 处理401错误
    if (error.response && error.response.status === 401) {
      removeToken();
      router.push("/login");
      return new Promise(() => {}); // 返回一个永远pending的promise，阻止后续操作
    }

    return Promise.reject(error);
  }
);

class PureHttp {
  // 通用请求方法
  public request<T>(
    method: RequestMethods,
    url: string,
    param?: AxiosRequestConfig,
    axiosConfig?: PureHttpRequestConfig
  ): Promise<T> {
    const config = {
      method,
      url,
      ...param,
      ...axiosConfig
    } as PureHttpRequestConfig;

    return instance.request(config);
  }

  // 单独抽离的post工具函数
  public post<T, P>(
    url: string,
    params?: AxiosRequestConfig<P>,
    config?: PureHttpRequestConfig
  ): Promise<T> {
    return this.request<T>("post", url, params, config);
  }

  // 单独抽离的get工具函数
  public get<T, P>(
    url: string,
    params?: AxiosRequestConfig<P>,
    config?: PureHttpRequestConfig
  ): Promise<T> {
    return this.request<T>("get", url, params, config);
  }
}

export const http = new PureHttp();
