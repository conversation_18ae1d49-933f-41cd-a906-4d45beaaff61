/**
 * 统一错误处理工具
 */
import { ElMessage } from "element-plus";

/**
 * API错误响应接口
 */
export interface ApiErrorResponse {
  code: number;
  msg: string;
  data?: any;
}

/**
 * 错误类型枚举
 */
export enum ErrorType {
  NETWORK = 'NETWORK',
  API = 'API',
  VALIDATION = 'VALIDATION',
  BUSINESS = 'BUSINESS',
  UNKNOWN = 'UNKNOWN'
}

/**
 * 错误信息接口
 */
export interface ErrorInfo {
  type: ErrorType;
  message: string;
  code?: number;
  details?: any;
}

/**
 * 默认错误消息映射
 */
const DEFAULT_ERROR_MESSAGES: Record<string, string> = {
  // 网络错误
  'Network Error': '网络连接失败，请检查网络设置',
  'timeout': '请求超时，请稍后重试',
  
  // HTTP状态码错误
  '400': '请求参数错误',
  '401': '未授权，请重新登录',
  '403': '权限不足',
  '404': '请求的资源不存在',
  '500': '服务器内部错误',
  '502': '网关错误',
  '503': '服务暂不可用',
  
  // 业务错误码
  '1001': '参数验证失败',
  '1002': '数据不存在',
  '1003': '操作失败',
  
  // 默认消息
  'default': '操作失败，请稍后重试'
};

/**
 * 解析错误信息
 * @param error 错误对象
 * @returns 解析后的错误信息
 */
export const parseError = (error: any): ErrorInfo => {
  // 网络错误
  if (error.message === 'Network Error') {
    return {
      type: ErrorType.NETWORK,
      message: DEFAULT_ERROR_MESSAGES['Network Error'],
      details: error
    };
  }
  
  // 超时错误
  if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
    return {
      type: ErrorType.NETWORK,
      message: DEFAULT_ERROR_MESSAGES['timeout'],
      details: error
    };
  }
  
  // HTTP状态码错误
  if (error.response && error.response.status) {
    const status = error.response.status.toString();
    return {
      type: ErrorType.API,
      message: DEFAULT_ERROR_MESSAGES[status] || `HTTP ${status} 错误`,
      code: error.response.status,
      details: error.response.data
    };
  }
  
  // API业务错误
  if (error.response && error.response.data) {
    const data = error.response.data;
    if (data.code && data.msg) {
      return {
        type: ErrorType.API,
        message: data.msg,
        code: data.code,
        details: data
      };
    }
  }
  
  // 直接的API错误响应
  if (error.code && error.msg) {
    return {
      type: ErrorType.API,
      message: error.msg,
      code: error.code,
      details: error
    };
  }
  
  // 验证错误
  if (error.name === 'ValidationError' || error.type === 'validation') {
    return {
      type: ErrorType.VALIDATION,
      message: error.message || '数据验证失败',
      details: error
    };
  }
  
  // 未知错误
  return {
    type: ErrorType.UNKNOWN,
    message: error.message || DEFAULT_ERROR_MESSAGES['default'],
    details: error
  };
};

/**
 * 显示错误消息
 * @param error 错误对象或错误信息
 * @param customMessage 自定义错误消息
 */
export const showError = (error: any, customMessage?: string) => {
  const errorInfo = parseError(error);
  const message = customMessage || errorInfo.message;
  
  ElMessage.error(message);
  
  // 在开发环境下输出详细错误信息
  if (process.env.NODE_ENV === 'development') {
    console.error('Error Details:', errorInfo);
  }
};

/**
 * 处理API响应
 * @param response API响应
 * @param successMessage 成功消息
 * @param errorMessage 错误消息
 * @returns 是否成功
 */
export const handleApiResponse = (
  response: any,
  successMessage?: string,
  errorMessage?: string
): boolean => {
  if (response && response.code === 0) {
    if (successMessage) {
      ElMessage.success(successMessage);
    }
    return true;
  } else {
    const message = errorMessage || response?.msg || '操作失败';
    ElMessage.error(message);
    return false;
  }
};

/**
 * 异步操作错误处理装饰器
 * @param operation 异步操作函数
 * @param errorMessage 自定义错误消息
 * @returns 包装后的函数
 */
export const withErrorHandling = <T extends (...args: any[]) => Promise<any>>(
  operation: T,
  errorMessage?: string
): T => {
  return (async (...args: any[]) => {
    try {
      return await operation(...args);
    } catch (error) {
      showError(error, errorMessage);
      throw error;
    }
  }) as T;
};

/**
 * 批量操作错误处理
 * @param operations 操作数组
 * @param itemName 操作项名称（用于错误消息）
 * @returns 操作结果
 */
export const handleBatchOperations = async <T>(
  operations: Promise<T>[],
  itemName: string = '项目'
): Promise<{ success: T[]; failed: any[]; }> => {
  const results = await Promise.allSettled(operations);
  
  const success: T[] = [];
  const failed: any[] = [];
  
  results.forEach((result, index) => {
    if (result.status === 'fulfilled') {
      success.push(result.value);
    } else {
      failed.push({
        index,
        error: result.reason
      });
    }
  });
  
  // 显示批量操作结果
  if (success.length > 0 && failed.length === 0) {
    ElMessage.success(`所有${itemName}操作成功`);
  } else if (success.length > 0 && failed.length > 0) {
    ElMessage.warning(`${success.length}个${itemName}操作成功，${failed.length}个失败`);
  } else if (failed.length > 0) {
    ElMessage.error(`所有${itemName}操作失败`);
  }
  
  return { success, failed };
};

/**
 * 表单提交错误处理
 * @param submitFn 提交函数
 * @param successMessage 成功消息
 * @param errorMessage 错误消息
 * @returns 包装后的提交函数
 */
export const handleFormSubmit = (
  submitFn: () => Promise<any>,
  successMessage?: string,
  errorMessage?: string
) => {
  return async () => {
    try {
      const result = await submitFn();
      
      if (result && typeof result === 'object' && 'code' in result) {
        return handleApiResponse(result, successMessage, errorMessage);
      } else {
        if (successMessage) {
          ElMessage.success(successMessage);
        }
        return true;
      }
    } catch (error) {
      showError(error, errorMessage);
      return false;
    }
  };
};
