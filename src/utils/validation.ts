/**
 * 通用验证工具函数
 */

/**
 * 验证正则表达式语法是否正确
 * @param pattern 正则表达式字符串
 * @returns 验证结果
 */
export const validateRegexPattern = (pattern: string): { valid: boolean; message?: string } => {
  if (!pattern || pattern.trim() === '') {
    return { valid: false, message: '正则表达式不能为空' };
  }

  try {
    new RegExp(pattern);
    return { valid: true };
  } catch (error) {
    return { valid: false, message: '正则表达式语法错误' };
  }
};

/**
 * 验证浮点数精度格式
 * @param scale 精度字符串，格式如 "10,2" 表示总长度10位，小数点后2位
 * @returns 验证结果
 */
export const validateFloatScale = (scale: string): { valid: boolean; message?: string } => {
  if (!scale || scale.trim() === '') {
    return { valid: false, message: '浮点数精度不能为空' };
  }

  // 支持两种格式：单个数字（如 "2"）或 "总位数,小数位数"（如 "10,2"）
  const scalePattern = /^(\d+)(,\d+)?$/;
  if (!scalePattern.test(scale.trim())) {
    return { valid: false, message: '浮点数精度格式错误，应为数字或"总位数,小数位数"格式' };
  }

  const parts = scale.split(',');
  if (parts.length === 2) {
    const totalDigits = parseInt(parts[0]);
    const decimalDigits = parseInt(parts[1]);
    
    if (decimalDigits >= totalDigits) {
      return { valid: false, message: '小数位数不能大于等于总位数' };
    }
    
    if (totalDigits > 65 || decimalDigits > 30) {
      return { valid: false, message: '精度超出允许范围' };
    }
  }

  return { valid: true };
};

/**
 * 验证长度范围
 * @param minLength 最小长度
 * @param maxLength 最大长度
 * @returns 验证结果
 */
export const validateLengthRange = (minLength: number, maxLength: number): { valid: boolean; message?: string } => {
  if (minLength < 0) {
    return { valid: false, message: '最小长度不能为负数' };
  }
  
  if (maxLength < 0) {
    return { valid: false, message: '最大长度不能为负数' };
  }
  
  if (minLength > maxLength) {
    return { valid: false, message: '最小长度不能大于最大长度' };
  }
  
  if (maxLength > 65535) {
    return { valid: false, message: '最大长度不能超过65535' };
  }
  
  return { valid: true };
};

/**
 * 验证字段名称格式
 * @param name 字段名称
 * @returns 验证结果
 */
export const validateFieldName = (name: string): { valid: boolean; message?: string } => {
  if (!name || name.trim() === '') {
    return { valid: false, message: '名称不能为空' };
  }
  
  const trimmedName = name.trim();
  
  // 检查长度
  if (trimmedName.length > 100) {
    return { valid: false, message: '名称长度不能超过100个字符' };
  }
  
  // 检查是否包含特殊字符（可根据实际需求调整）
  const invalidChars = /[<>'"&]/;
  if (invalidChars.test(trimmedName)) {
    return { valid: false, message: '名称不能包含特殊字符 < > \' " &' };
  }
  
  return { valid: true };
};

/**
 * 验证缩写格式
 * @param shortName 缩写
 * @returns 验证结果
 */
export const validateShortName = (shortName: string): { valid: boolean; message?: string } => {
  if (!shortName || shortName.trim() === '') {
    return { valid: false, message: '缩写不能为空' };
  }
  
  const trimmedShortName = shortName.trim();
  
  // 检查长度
  if (trimmedShortName.length > 50) {
    return { valid: false, message: '缩写长度不能超过50个字符' };
  }
  
  // 缩写通常应该是字母、数字、下划线的组合
  const shortNamePattern = /^[a-zA-Z0-9_]+$/;
  if (!shortNamePattern.test(trimmedShortName)) {
    return { valid: false, message: '缩写只能包含字母、数字和下划线' };
  }
  
  return { valid: true };
};

/**
 * Element Plus 表单验证规则生成器
 */
export const createValidationRules = () => {
  return {
    // 名称验证规则
    name: [
      { required: true, message: '请输入名称', trigger: 'blur' },
      {
        validator: (rule: any, value: string, callback: Function) => {
          const result = validateFieldName(value);
          if (result.valid) {
            callback();
          } else {
            callback(new Error(result.message));
          }
        },
        trigger: 'blur'
      }
    ],
    
    // 缩写验证规则
    shortName: [
      { required: true, message: '请输入缩写', trigger: 'blur' },
      {
        validator: (rule: any, value: string, callback: Function) => {
          const result = validateShortName(value);
          if (result.valid) {
            callback();
          } else {
            callback(new Error(result.message));
          }
        },
        trigger: 'blur'
      }
    ],
    
    // 目录验证规则
    dir: [
      { required: true, message: '请选择所属目录', trigger: 'change' }
    ],
    
    // 数据类型验证规则
    dataType: [
      { required: true, message: '请选择数据类型', trigger: 'change' }
    ],
    
    // 长度验证规则
    minLength: [
      { required: true, message: '请输入最小长度', trigger: 'blur' },
      { type: 'number', min: 0, message: '最小长度不能为负数', trigger: 'blur' }
    ],
    
    maxLength: [
      { required: true, message: '请输入最大长度', trigger: 'blur' },
      { type: 'number', min: 0, max: 65535, message: '最大长度范围为0-65535', trigger: 'blur' }
    ],
    
    // 浮点数精度验证规则
    scale: [
      {
        validator: (rule: any, value: string, callback: Function) => {
          // 只有当数据类型为浮点数时才验证
          const form = rule.fullField ? rule.fullField.split('.')[0] : null;
          if (form && form.dataType === 1) {
            const result = validateFloatScale(value);
            if (result.valid) {
              callback();
            } else {
              callback(new Error(result.message));
            }
          } else {
            callback();
          }
        },
        trigger: 'blur'
      }
    ],
    
    // 正则表达式验证规则
    regexPattern: [
      {
        validator: (rule: any, value: string, callback: Function) => {
          if (value && value.trim()) {
            const result = validateRegexPattern(value);
            if (result.valid) {
              callback();
            } else {
              callback(new Error(result.message));
            }
          } else {
            callback();
          }
        },
        trigger: 'blur'
      }
    ]
  };
};
