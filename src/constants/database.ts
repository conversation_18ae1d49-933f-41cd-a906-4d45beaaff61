export const DATABASE_TYPES = [
  { label: "未知", value: "0" },
  { label: "MySQL", value: "1" },
  { label: "Oracle", value: "2" },
  { label: "SQLServer2000", value: "3" },
  { label: "SQLServer", value: "4" },
  { label: "PostgreSQL", value: "5" },
  { label: "GreenPlum", value: "6" },
  { label: "MariaDB", value: "7" },
  { label: "DB2", value: "8" },
  { label: "达梦", value: "9" },
  { label: "人大金仓", value: "10" },
  { label: "神通数据库", value: "11" },
  { label: "南大通用GBase", value: "12" },
  { label: "Hive", value: "13" },
  { label: "SQLite", value: "14" },
  { label: "Sybase", value: "15" },
  { label: "Doris", value: "16" },
  { label: "ClickHouse", value: "17" },
  { label: "MongoDB", value: "18" },
  { label: "TiDB", value: "19" }
] as const;

// 数据库类型的值到标签的映射
export const DATABASE_TYPE_MAP = DATABASE_TYPES.reduce(
  (acc, { label, value }) => {
    acc[value] = label;
    return acc;
  },
  {} as Record<string, string>
);

// 类型定义
export type DatabaseType = (typeof DATABASE_TYPES)[number]["value"];
