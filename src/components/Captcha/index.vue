<template>
  <div class="captcha-container" @click="refreshCode">
    <img v-if="imageUrl" :src="imageUrl" :width="width" :height="height" />
    <canvas v-else ref="captchaCanvas" :width="width" :height="height"></canvas>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from "vue";
import { getCaptcha } from "@/api/captcha";

const props = defineProps({
  width: {
    type: Number,
    default: 120
  },
  height: {
    type: Number,
    default: 40
  },
  modelValue: {
    type: String,
    default: ""
  }
});

const emit = defineEmits(["update:modelValue"]);
const captchaCanvas = ref<HTMLCanvasElement | null>(null);
const imageUrl = ref("");

// 生成随机验证码
const generateCode = () => {
  const characters = "ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789";
  let result = "";
  for (let i = 0; i < 4; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return result;
};

// 绘制验证码
const drawCaptcha = () => {
  const canvas = captchaCanvas.value;
  if (!canvas) return;

  const ctx = canvas.getContext("2d");
  if (!ctx) return;

  // 生成新的验证码
  const key = generateCode();
  emit("update:modelValue", key);

  // 清空画布
  ctx.fillStyle = "#f5f5f5";
  ctx.fillRect(0, 0, props.width, props.height);

  // 绘制文字
  ctx.font = "26px Arial";
  ctx.textBaseline = "middle";

  // 随机颜色和位置绘制每个字符
  for (let i = 0; i < key.length; i++) {
    ctx.fillStyle = `rgb(${Math.random() * 100}, ${Math.random() * 100}, ${Math.random() * 100})`;
    ctx.setTransform(
      1,
      Math.random() * 0.2 - 0.1,
      Math.random() * 0.2 - 0.1,
      1,
      (i + 1) * 25,
      props.height / 2
    );
    ctx.fillText(key[i], 0, 0);
  }

  // 添加干扰线
  ctx.setTransform(1, 0, 0, 1, 0, 0);
  for (let i = 0; i < 3; i++) {
    ctx.strokeStyle = `rgb(${Math.random() * 200}, ${Math.random() * 200}, ${Math.random() * 200})`;
    ctx.beginPath();
    ctx.moveTo(Math.random() * props.width, Math.random() * props.height);
    ctx.lineTo(Math.random() * props.width, Math.random() * props.height);
    ctx.stroke();
  }
};

// 设置验证码图片
const setImage = (image: string) => {
  imageUrl.value = image;
};

// 刷新验证码
const refreshCode = async () => {
  try {
    const res = await getCaptcha();
    if (res.code === 0) {
      emit("update:modelValue", res.data.key);
      setImage(res.data.image);
    }
  } catch (error) {
    console.error("获取验证码失败:", error);
  }
};

onMounted(() => {
  refreshCode();
});

defineExpose({
  refreshCode,
  setImage
});
</script>

<style scoped>
.captcha-container {
  cursor: pointer;
  display: inline-block;
}

canvas {
  border-radius: 4px;
}
</style>
